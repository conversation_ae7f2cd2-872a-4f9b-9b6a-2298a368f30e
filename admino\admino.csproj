<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{89C09A08-67EE-4E93-9324-33B34C693FBE}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>admino</RootNamespace>
    <AssemblyName>admino</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Guna.UI2, Version=2.0.4.7, Culture=neutral, PublicKeyToken=8b9d14aa5142e261, processorArchitecture=MSIL">
      <HintPath>..\packages\Guna.UI2.WinForms.2.0.4.7\lib\net48\Guna.UI2.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Design" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="forma\add pharmacy.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="forma\add pharmacy.Designer.cs">
      <DependentUpon>add pharmacy.cs</DependentUpon>
    </Compile>
    <Compile Include="forma\uc_pharmacylist.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="forma\uc_pharmacylist.Designer.cs">
      <DependentUpon>uc_pharmacylist.cs</DependentUpon>
    </Compile>
    <Compile Include="forma\uc_dashbord.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="forma\uc_dashbord.Designer.cs">
      <DependentUpon>uc_dashbord.cs</DependentUpon>
    </Compile>
    <Compile Include="DatabaseConnection.cs" />
    <Compile Include="DatabaseTest.cs" />
    <Compile Include="QuickTest.cs" />
    <Compile Include="AdminUserManager.cs" />
    <Compile Include="PharmacyManager.cs" />
    <Compile Include="PharmacyInfo.cs" />
    <Compile Include="PharmacyUser.cs" />
    <Compile Include="PharmacyUserManager.cs" />
    <Compile Include="forma\PharmacyUsersControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="forma\AddEditPharmacyUserForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="forma\DatabaseManagerControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="forma\DatabaseRecordEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="forma\SubscriptionManagementControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="forma\UpdateSubscriptionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="all.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="all.Designer.cs">
      <DependentUpon>all.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="forma\add pharmacy.resx">
      <DependentUpon>add pharmacy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="forma\uc_pharmacylist.resx">
      <DependentUpon>uc_pharmacylist.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="forma\uc_dashbord.resx">
      <DependentUpon>uc_dashbord.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="all.resx">
      <DependentUpon>all.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <EmbeddedResource Include="TestForm.resx">
      <DependentUpon>TestForm.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>