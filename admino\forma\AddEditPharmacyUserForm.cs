using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class AddEditPharmacyUserForm : Form
    {
        private Guna2Panel mainPanel;
        private Guna2Panel headerPanel;
        private Guna2Panel contentPanel;
        private Guna2Panel buttonPanel;
        
        private Label lblTitle;
        private Label lblPharmacy;
        private Label lblUsername;
        private Label lblPassword;
        private Label lblConfirmPassword;
        private Label lblFullName;
        private Label lblFullNameAr;
        private Label lblEmail;
        private Label lblDateOfBirth;
        private Label lblMobile;
        private Label lblRole;
        private Label lblNotes;
        
        private Guna2ComboBox cmbPharmacy;
        private Guna2TextBox txtUsername;
        private Guna2TextBox txtPassword;
        private Guna2TextBox txtConfirmPassword;
        private Guna2TextBox txtFullName;
        private Guna2TextBox txtFullNameAr;
        private Guna2TextBox txtEmail;
        private Guna2DateTimePicker dtpDateOfBirth;
        private Guna2TextBox txtMobile;
        private Guna2ComboBox cmbRole;
        private Guna2TextBox txtNotes;
        private Guna2CheckBox chkIsActive;
        
        private Guna2Button btnSave;
        private Guna2Button btnCancel;
        
        private bool isEditMode = false;
        private int userId = 0;
        private PharmacyUser currentUser;

        public AddEditPharmacyUserForm(int userId = 0)
        {
            this.userId = userId;
            this.isEditMode = userId > 0;
            
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
            
            if (isEditMode)
            {
                LoadUserData();
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form settings
            this.Size = new Size(900, 750);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.ShowInTaskbar = false;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Text = isEditMode ? "تعديل مستخدم الصيدلية" : "إضافة مستخدم جديد";

            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Main Panel
            mainPanel = new Guna2Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 244, 247),
                Padding = new Padding(20)
            };
            this.Controls.Add(mainPanel);

            // Header Panel
            headerPanel = new Guna2Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                BorderRadius = 10
            };
            headerPanel.ShadowDecoration.Enabled = true;
            mainPanel.Controls.Add(headerPanel);

            // Title
            lblTitle = new Label
            {
                Text = isEditMode ? "تعديل مستخدم الصيدلية" : "إضافة مستخدم جديد",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Location = new Point(30, 25),
                AutoSize = true
            };
            headerPanel.Controls.Add(lblTitle);

            // Content Panel
            contentPanel = new Guna2Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                BorderRadius = 10,
                AutoScroll = true
            };
            contentPanel.ShadowDecoration.Enabled = true;
            contentPanel.Margin = new Padding(0, 10, 0, 0);
            mainPanel.Controls.Add(contentPanel);

            // Button Panel
            buttonPanel = new Guna2Panel
            {
                Height = 70,
                Dock = DockStyle.Bottom,
                BackColor = Color.White,
                BorderRadius = 10
            };
            buttonPanel.ShadowDecoration.Enabled = true;
            buttonPanel.Margin = new Padding(0, 10, 0, 0);
            mainPanel.Controls.Add(buttonPanel);

            InitializeFormControls();
            InitializeButtons();
        }

        private void InitializeFormControls()
        {
            int startY = 30;
            int labelHeight = 25;
            int controlHeight = 40;
            int spacing = 25;
            int leftColumnX = 30;
            int rightColumnX = 450;
            int controlWidth = 380;

            // Row 1: Pharmacy and Role
            lblPharmacy = CreateLabel("الصيدلية:", leftColumnX, startY);
            cmbPharmacy = CreateComboBox(leftColumnX, startY + labelHeight, controlWidth);
            cmbPharmacy.ForeColor = Color.FromArgb(44, 62, 80);
            cmbPharmacy.Font = new Font("Segoe UI", 11F);
            contentPanel.Controls.AddRange(new Control[] { lblPharmacy, cmbPharmacy });

            lblRole = CreateLabel("الدور:", rightColumnX, startY);
            cmbRole = CreateComboBox(rightColumnX, startY + labelHeight, controlWidth);
            cmbRole.ForeColor = Color.FromArgb(44, 62, 80);
            cmbRole.Font = new Font("Segoe UI", 11F);
            contentPanel.Controls.AddRange(new Control[] { lblRole, cmbRole });

            startY += labelHeight + controlHeight + spacing;

            // Row 2: Username and Email
            lblUsername = CreateLabel("اسم المستخدم:", leftColumnX, startY);
            txtUsername = CreateTextBox(leftColumnX, startY + labelHeight, controlWidth, "أدخل اسم المستخدم");
            contentPanel.Controls.AddRange(new Control[] { lblUsername, txtUsername });

            lblEmail = CreateLabel("البريد الإلكتروني:", rightColumnX, startY);
            txtEmail = CreateTextBox(rightColumnX, startY + labelHeight, controlWidth, "أدخل البريد الإلكتروني");
            contentPanel.Controls.AddRange(new Control[] { lblEmail, txtEmail });

            startY += labelHeight + controlHeight + spacing;

            // Row 3: Password and Confirm Password
            lblPassword = CreateLabel("كلمة المرور:", leftColumnX, startY);
            txtPassword = CreateTextBox(leftColumnX, startY + labelHeight, controlWidth, "أدخل كلمة المرور");
            txtPassword.PasswordChar = '*';
            contentPanel.Controls.AddRange(new Control[] { lblPassword, txtPassword });

            lblConfirmPassword = CreateLabel("تأكيد كلمة المرور:", rightColumnX, startY);
            txtConfirmPassword = CreateTextBox(rightColumnX, startY + labelHeight, controlWidth, "أعد إدخال كلمة المرور");
            txtConfirmPassword.PasswordChar = '*';
            contentPanel.Controls.AddRange(new Control[] { lblConfirmPassword, txtConfirmPassword });

            startY += labelHeight + controlHeight + spacing;

            // Row 4: Full Name and Full Name Arabic
            lblFullName = CreateLabel("الاسم الكامل (إنجليزي):", leftColumnX, startY);
            txtFullName = CreateTextBox(leftColumnX, startY + labelHeight, controlWidth, "أدخل الاسم الكامل بالإنجليزية");
            contentPanel.Controls.AddRange(new Control[] { lblFullName, txtFullName });

            lblFullNameAr = CreateLabel("الاسم الكامل (عربي):", rightColumnX, startY);
            txtFullNameAr = CreateTextBox(rightColumnX, startY + labelHeight, controlWidth, "أدخل الاسم الكامل بالعربية");
            contentPanel.Controls.AddRange(new Control[] { lblFullNameAr, txtFullNameAr });

            startY += labelHeight + controlHeight + spacing;

            // Row 5: Date of Birth and Mobile
            lblDateOfBirth = CreateLabel("تاريخ الميلاد:", leftColumnX, startY);
            dtpDateOfBirth = CreateDateTimePicker(leftColumnX, startY + labelHeight, controlWidth);
            contentPanel.Controls.AddRange(new Control[] { lblDateOfBirth, dtpDateOfBirth });

            lblMobile = CreateLabel("الجوال:", rightColumnX, startY);
            txtMobile = CreateTextBox(rightColumnX, startY + labelHeight, controlWidth, "أدخل رقم الجوال");
            contentPanel.Controls.AddRange(new Control[] { lblMobile, txtMobile });

            startY += labelHeight + controlHeight + spacing;

            // Row 6: Notes (full width)
            lblNotes = CreateLabel("ملاحظات:", leftColumnX, startY);
            txtNotes = CreateTextBox(leftColumnX, startY + labelHeight, controlWidth * 2 + (rightColumnX - leftColumnX - controlWidth), "أدخل أي ملاحظات إضافية");
            txtNotes.Multiline = true;
            txtNotes.Height = 80;
            contentPanel.Controls.AddRange(new Control[] { lblNotes, txtNotes });

            startY += labelHeight + 80 + spacing;

            // Row 7: Is Active Checkbox
            chkIsActive = new Guna2CheckBox
            {
                Location = new Point(leftColumnX, startY),
                Size = new Size(200, 30),
                Text = "المستخدم نشط",
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Checked = true
            };
            contentPanel.Controls.Add(chkIsActive);
        }

        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(380, 25),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleLeft
            };
        }

        private Guna2TextBox CreateTextBox(int x, int y, int width, string placeholder)
        {
            return new Guna2TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 40),
                PlaceholderText = placeholder,
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(44, 62, 80),
                BorderColor = Color.FromArgb(213, 218, 223)
            };
        }

        private Guna2ComboBox CreateComboBox(int x, int y, int width)
        {
            return new Guna2ComboBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                ForeColor = Color.FromArgb(44, 62, 80),
                BackColor = Color.White,
                BorderColor = Color.FromArgb(213, 218, 223),
                FillColor = Color.White
            };
        }

        private Guna2DateTimePicker CreateDateTimePicker(int x, int y, int width)
        {
            return new Guna2DateTimePicker
            {
                Location = new Point(x, y),
                Size = new Size(width, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                Format = DateTimePickerFormat.Short,
                Value = new DateTime(1990, 1, 1),
                MaxDate = DateTime.Today.AddYears(-16), // At least 16 years old
                MinDate = DateTime.Today.AddYears(-80),  // Maximum 80 years old
                ForeColor = Color.FromArgb(44, 62, 80)
            };
        }

        private void InitializeButtons()
        {
            int buttonY = 15;
            int buttonHeight = 40;
            int buttonWidth = 120;
            int spacing = 20;

            // Save Button
            btnSave = new Guna2Button
            {
                Location = new Point(30, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = isEditMode ? "تحديث" : "حفظ",
                BorderRadius = 8,
                FillColor = Color.FromArgb(46, 204, 113),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnSave.Click += BtnSave_Click;
            buttonPanel.Controls.Add(btnSave);

            // Cancel Button
            btnCancel = new Guna2Button
            {
                Location = new Point(30 + buttonWidth + spacing, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = "إلغاء",
                BorderRadius = 8,
                FillColor = Color.FromArgb(149, 165, 166),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnCancel.Click += BtnCancel_Click;
            buttonPanel.Controls.Add(btnCancel);
        }

        private void LoadData()
        {
            try
            {
                // Load pharmacies
                var pharmacies = PharmacyManager.GetAllPharmacies();
                cmbPharmacy.Items.Clear();
                if (pharmacies != null && pharmacies.Rows.Count > 0)
                {
                    foreach (DataRow row in pharmacies.Rows)
                    {
                        string pharmacyName = row["pharmacyNameAr"]?.ToString() ?? row["pharmacyName"]?.ToString() ?? "صيدلية غير محددة";
                        string pharmacyCode = row["pharmacyCode"]?.ToString() ?? "";
                        cmbPharmacy.Items.Add(new ComboBoxItem
                        {
                            Text = $"{pharmacyName} ({pharmacyCode})",
                            Value = Convert.ToInt32(row["id"])
                        });
                    }
                }

                // Load roles - simplified approach
                cmbRole.Items.Clear();
                cmbRole.Items.Add(new ComboBoxItem { Text = "Administrator", Value = "Administrator" });
                cmbRole.Items.Add(new ComboBoxItem { Text = "Employee", Value = "Employee" });
                cmbRole.Items.Add(new ComboBoxItem { Text = "Pharmacist", Value = "Pharmacist" });

                // Try to load from database if available
                try
                {
                    var roles = PharmacyUserManager.GetAllUserRoles();
                    if (roles != null && roles.Rows.Count > 0)
                    {
                        cmbRole.Items.Clear();
                        foreach (DataRow row in roles.Rows)
                        {
                            string roleName = row["roleNameAr"]?.ToString() ?? row["roleName"]?.ToString();
                            if (!string.IsNullOrEmpty(roleName))
                            {
                                cmbRole.Items.Add(new ComboBoxItem
                                {
                                    Text = roleName,
                                    Value = roleName
                                });
                            }
                        }
                    }
                }
                catch
                {
                    // Keep default roles if database query fails
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Load default roles as fallback
                cmbRole.Items.Clear();
                cmbRole.Items.Add(new ComboBoxItem { Text = "Administrator", Value = "Administrator" });
                cmbRole.Items.Add(new ComboBoxItem { Text = "Employee", Value = "Employee" });
                cmbRole.Items.Add(new ComboBoxItem { Text = "Pharmacist", Value = "Pharmacist" });
            }
        }

        private void LoadUserData()
        {
            try
            {
                var users = PharmacyUserManager.GetAllPharmacyUsers();
                var userRow = users.AsEnumerable().FirstOrDefault(r => Convert.ToInt32(r["id"]) == userId);

                if (userRow != null)
                {
                    // Set pharmacy
                    int pharmacyId = Convert.ToInt32(userRow["pharmacyId"]);
                    for (int i = 0; i < cmbPharmacy.Items.Count; i++)
                    {
                        if (((ComboBoxItem)cmbPharmacy.Items[i]).Value.Equals(pharmacyId))
                        {
                            cmbPharmacy.SelectedIndex = i;
                            break;
                        }
                    }

                    // Set role
                    string roleName = userRow["roleNameAr"]?.ToString() ?? userRow["roleName"]?.ToString() ?? "";
                    for (int i = 0; i < cmbRole.Items.Count; i++)
                    {
                        if (((ComboBoxItem)cmbRole.Items[i]).Text == roleName ||
                            ((ComboBoxItem)cmbRole.Items[i]).Value.ToString() == roleName)
                        {
                            cmbRole.SelectedIndex = i;
                            break;
                        }
                    }

                    // Set other fields
                    txtUsername.Text = userRow["username"].ToString();
                    txtFullName.Text = userRow["fullName"].ToString();
                    txtFullNameAr.Text = userRow["fullNameAr"].ToString();
                    txtEmail.Text = userRow["email"].ToString();
                    txtMobile.Text = userRow["mobile"]?.ToString() ?? "";
                    chkIsActive.Checked = Convert.ToBoolean(userRow["isActive"]);

                    // Set date of birth if available
                    if (userRow["dob"] != null && userRow["dob"] != DBNull.Value)
                    {
                        if (DateTime.TryParse(userRow["dob"].ToString(), out DateTime dob))
                        {
                            dtpDateOfBirth.Value = dob;
                        }
                    }

                    // Disable username in edit mode
                    txtUsername.Enabled = false;

                    // Make password optional in edit mode
                    lblPassword.Text = "كلمة المرور الجديدة (اختياري):";
                    lblConfirmPassword.Text = "تأكيد كلمة المرور الجديدة:";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المستخدم: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var user = new PharmacyUser
                {
                    Id = userId,
                    PharmacyId = cmbPharmacy.SelectedItem != null ? (int)((ComboBoxItem)cmbPharmacy.SelectedItem).Value : 0,
                    Username = txtUsername.Text.Trim(),
                    FullName = txtFullName.Text.Trim(),
                    FullNameAr = txtFullNameAr.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Mobile = txtMobile.Text.Trim(),
                    DateOfBirth = dtpDateOfBirth.Value,
                    RoleName = cmbRole.SelectedItem != null ? ((ComboBoxItem)cmbRole.SelectedItem).Value.ToString() : "Employee",
                    RoleNameAr = cmbRole.SelectedItem != null ? ((ComboBoxItem)cmbRole.SelectedItem).Text : "Employee",
                    IsActive = chkIsActive.Checked,
                    Notes = txtNotes.Text.Trim()
                };

                // Set password only if provided
                if (!string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    user.Password = txtPassword.Text;
                }

                bool success;
                if (isEditMode)
                {
                    user.ModifiedBy = PharmacyUserManager.CurrentUser?.Id ?? 1; // Default to admin if null
                    success = PharmacyUserManager.UpdatePharmacyUser(user);
                }
                else
                {
                    user.Password = txtPassword.Text; // Required for new users
                    user.CreatedBy = PharmacyUserManager.CurrentUser?.Id ?? 1; // Default to admin if null
                    success = PharmacyUserManager.AddPharmacyUser(user);
                }

                if (success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ بيانات المستخدم", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            // Pharmacy validation
            if (cmbPharmacy.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار الصيدلية", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbPharmacy.Focus();
                return false;
            }

            // Role validation
            if (cmbRole.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار الدور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbRole.Focus();
                return false;
            }

            // Username validation
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUsername.Focus();
                return false;
            }

            // Password validation (required for new users)
            if (!isEditMode && string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPassword.Focus();
                return false;
            }

            // Password confirmation
            if (!string.IsNullOrWhiteSpace(txtPassword.Text) && txtPassword.Text != txtConfirmPassword.Text)
            {
                MessageBox.Show("كلمة المرور وتأكيد كلمة المرور غير متطابقتين", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtConfirmPassword.Focus();
                return false;
            }

            // Full Name validation
            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل بالإنجليزية", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFullName.Focus();
                return false;
            }

            // Full Name Arabic validation
            if (string.IsNullOrWhiteSpace(txtFullNameAr.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل بالعربية", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFullNameAr.Focus();
                return false;
            }

            // Email validation
            if (string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال البريد الإلكتروني", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            // Email format validation
            if (!IsValidEmail(txtEmail.Text))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        // Helper class for ComboBox items
        public class ComboBoxItem
        {
            public string Text { get; set; }
            public object Value { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }
    }
}
   
