using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class DatabaseRecordEditorForm : Form
    {
        private string tableName;
        private DataRow recordData;
        private bool isEditMode;
        private Dictionary<string, Control> fieldControls;
        private DataTable tableSchema;
        
        private Guna2Panel pnlMain;
        private Guna2Panel pnlButtons;
        private Guna2Button btnSave;
        private Guna2Button btnCancel;
        private Label lblTitle;
        private TableLayoutPanel tlpFields;

        public DatabaseRecordEditorForm(string tableName, DataRow recordData, bool isEditMode)
        {
            this.tableName = tableName;
            this.recordData = recordData;
            this.isEditMode = isEditMode;
            this.fieldControls = new Dictionary<string, Control>();
            
            InitializeComponent();
            LoadTableSchema();
            CreateFieldControls();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = isEditMode ? "تعديل السجل" : "إضافة سجل جديد";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.BackColor = Color.White;
            
            // Create panels
            CreatePanels();
            CreateButtons();
            
            this.ResumeLayout(false);
        }

        private void CreatePanels()
        {
            // Title label
            lblTitle = new Label
            {
                Text = $"{(isEditMode ? "تعديل" : "إضافة")} سجل في جدول {tableName}",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                Height = 40,
                Dock = DockStyle.Top,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(247, 248, 250)
            };
            this.Controls.Add(lblTitle);

            // Button panel
            pnlButtons = new Guna2Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom,
                FillColor = Color.FromArgb(247, 248, 250)
            };
            this.Controls.Add(pnlButtons);

            // Main panel with scroll
            pnlMain = new Guna2Panel
            {
                Dock = DockStyle.Fill,
                FillColor = Color.White,
                AutoScroll = true,
                Padding = new Padding(20)
            };
            this.Controls.Add(pnlMain);

            // Table layout for fields
            tlpFields = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 2,
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink
            };
            tlpFields.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F));
            tlpFields.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 70F));
            pnlMain.Controls.Add(tlpFields);
        }

        private void CreateButtons()
        {
            // Save button
            btnSave = new Guna2Button
            {
                Text = "حفظ",
                Size = new Size(100, 35),
                Location = new Point(20, 12),
                BorderRadius = 8,
                FillColor = Color.FromArgb(94, 148, 255),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White
            };
            btnSave.Click += BtnSave_Click;
            pnlButtons.Controls.Add(btnSave);

            // Cancel button
            btnCancel = new Guna2Button
            {
                Text = "إلغاء",
                Size = new Size(100, 35),
                Location = new Point(140, 12),
                BorderRadius = 8,
                FillColor = Color.FromArgb(108, 117, 125),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White
            };
            btnCancel.Click += BtnCancel_Click;
            pnlButtons.Controls.Add(btnCancel);
        }

        private void LoadTableSchema()
        {
            try
            {
                string query = $@"
                    SELECT 
                        COLUMN_NAME,
                        DATA_TYPE,
                        IS_NULLABLE,
                        COLUMN_DEFAULT,
                        CHARACTER_MAXIMUM_LENGTH,
                        NUMERIC_PRECISION,
                        NUMERIC_SCALE
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = '{tableName}'
                    ORDER BY ORDINAL_POSITION";

                tableSchema = DatabaseConnection.ExecutePharmacyQuery(query);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بنية الجدول: {ex.Message}", "خطأ", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }

        private void CreateFieldControls()
        {
            if (tableSchema == null) return;

            int rowIndex = 0;
            
            foreach (DataRow schemaRow in tableSchema.Rows)
            {
                string columnName = schemaRow["COLUMN_NAME"].ToString();
                string dataType = schemaRow["DATA_TYPE"].ToString();
                bool isNullable = schemaRow["IS_NULLABLE"].ToString() == "YES";
                int? maxLength = schemaRow["CHARACTER_MAXIMUM_LENGTH"] as int?;

                // Skip identity columns in add mode
                if (!isEditMode && columnName.ToLower() == "id")
                    continue;

                // Create label
                Label label = new Label
                {
                    Text = GetArabicColumnName(columnName) + (isNullable ? "" : " *"),
                    Font = new Font("Segoe UI", 10),
                    ForeColor = Color.FromArgb(64, 64, 64),
                    TextAlign = ContentAlignment.MiddleRight,
                    Anchor = AnchorStyles.Right,
                    AutoSize = true
                };

                // Create appropriate control based on data type
                Control fieldControl = CreateControlForDataType(columnName, dataType, maxLength, isNullable);
                
                if (fieldControl != null)
                {
                    fieldControls[columnName] = fieldControl;
                    
                    // Add to table layout
                    tlpFields.RowCount = rowIndex + 1;
                    tlpFields.RowStyles.Add(new RowStyle(SizeType.AutoSize));
                    
                    tlpFields.Controls.Add(label, 0, rowIndex);
                    tlpFields.Controls.Add(fieldControl, 1, rowIndex);
                    
                    rowIndex++;
                }
            }
        }

        private Control CreateControlForDataType(string columnName, string dataType, int? maxLength, bool isNullable)
        {
            Control control = null;
            
            switch (dataType.ToLower())
            {
                case "bit":
                    control = new Guna2CheckBox
                    {
                        Name = columnName,
                        Size = new Size(100, 25),
                        Font = new Font("Segoe UI", 10)
                    };
                    break;

                case "datetime":
                case "datetime2":
                case "date":
                    control = new Guna2DateTimePicker
                    {
                        Name = columnName,
                        Size = new Size(200, 30),
                        Font = new Font("Segoe UI", 10),
                        Format = DateTimePickerFormat.Short,
                        ShowCheckBox = isNullable
                    };
                    break;

                case "int":
                case "bigint":
                case "smallint":
                case "tinyint":
                    control = new Guna2NumericUpDown
                    {
                        Name = columnName,
                        Size = new Size(150, 30),
                        Font = new Font("Segoe UI", 10),
                        Minimum = 0,
                        Maximum = dataType == "int" ? int.MaxValue : 999999999
                    };
                    break;

                case "decimal":
                case "numeric":
                case "float":
                case "real":
                    control = new Guna2TextBox
                    {
                        Name = columnName,
                        Size = new Size(150, 30),
                        Font = new Font("Segoe UI", 10),
                        BorderRadius = 8
                    };
                    break;

                case "nvarchar":
                case "varchar":
                case "nchar":
                case "char":
                case "ntext":
                case "text":
                    if (maxLength > 100 || maxLength == -1)
                    {
                        // Multi-line text box for long text
                        control = new Guna2TextBox
                        {
                            Name = columnName,
                            Size = new Size(300, 80),
                            Font = new Font("Segoe UI", 10),
                            BorderRadius = 8,
                            Multiline = true,
                            ScrollBars = ScrollBars.Vertical
                        };
                    }
                    else
                    {
                        // Single-line text box
                        control = new Guna2TextBox
                        {
                            Name = columnName,
                            Size = new Size(250, 30),
                            Font = new Font("Segoe UI", 10),
                            BorderRadius = 8,
                            MaxLength = maxLength ?? 255
                        };
                    }
                    break;

                default:
                    // Default to text box
                    control = new Guna2TextBox
                    {
                        Name = columnName,
                        Size = new Size(250, 30),
                        Font = new Font("Segoe UI", 10),
                        BorderRadius = 8
                    };
                    break;
            }

            // Disable ID field in edit mode
            if (isEditMode && columnName.ToLower() == "id" && control != null)
            {
                control.Enabled = false;
            }

            return control;
        }

        private string GetArabicColumnName(string columnName)
        {
            // Dictionary for common column name translations
            Dictionary<string, string> translations = new Dictionary<string, string>
            {
                {"id", "المعرف"},
                {"pharmacyId", "معرف الصيدلية"},
                {"pharmacyCode", "كود الصيدلية"},
                {"pharmacyName", "اسم الصيدلية (EN)"},
                {"pharmacyNameAr", "اسم الصيدلية"},
                {"ownerName", "اسم المالك (EN)"},
                {"ownerNameAr", "اسم المالك"},
                {"licenseNumber", "رقم الترخيص"},
                {"taxNumber", "الرقم الضريبي"},
                {"email", "البريد الإلكتروني"},
                {"phone", "الهاتف"},
                {"mobile", "الجوال"},
                {"address", "العنوان (EN)"},
                {"addressAr", "العنوان"},
                {"city", "المدينة (EN)"},
                {"cityAr", "المدينة"},
                {"region", "المنطقة (EN)"},
                {"regionAr", "المنطقة"},
                {"postalCode", "الرمز البريدي"},
                {"latitude", "خط العرض"},
                {"longitude", "خط الطول"},
                {"status", "الحالة"},
                {"isActive", "نشط"},
                {"registrationDate", "تاريخ التسجيل"},
                {"approvalDate", "تاريخ الموافقة"},
                {"approvedBy", "تمت الموافقة بواسطة"},
                {"lastActivityDate", "تاريخ آخر نشاط"},
                {"subscriptionStatus", "حالة الاشتراك"},
                {"subscriptionStartDate", "تاريخ بداية الاشتراك"},
                {"subscriptionEndDate", "تاريخ انتهاء الاشتراك"},
                {"notes", "ملاحظات"},
                {"username", "اسم المستخدم"},
                {"password", "كلمة المرور"},
                {"fullName", "الاسم الكامل (EN)"},
                {"fullNameAr", "الاسم الكامل"},
                {"roleId", "معرف الدور"},
                {"roleName", "اسم الدور (EN)"},
                {"roleNameAr", "اسم الدور"},
                {"lastLogin", "آخر تسجيل دخول"},
                {"loginAttempts", "محاولات تسجيل الدخول"},
                {"lockedUntil", "مقفل حتى"},
                {"createdDate", "تاريخ الإنشاء"},
                {"createdBy", "أنشئ بواسطة"},
                {"modifiedDate", "تاريخ التعديل"},
                {"modifiedBy", "عدل بواسطة"},
                {"profileImage", "صورة الملف الشخصي"},
                {"sessionToken", "رمز الجلسة"},
                {"sessionExpiry", "انتهاء الجلسة"},
                {"permissionName", "اسم الصلاحية (EN)"},
                {"permissionNameAr", "اسم الصلاحية"},
                {"description", "الوصف (EN)"},
                {"descriptionAr", "الوصف"},
                {"category", "الفئة"},
                {"permissions", "الصلاحيات"},
                {"userId", "معرف المستخدم"},
                {"permissionId", "معرف الصلاحية"},
                {"grantedBy", "منح بواسطة"},
                {"grantedDate", "تاريخ المنح"},
                {"expiryDate", "تاريخ الانتهاء"},
                {"ipAddress", "عنوان IP"},
                {"userAgent", "وكيل المستخدم"},
                {"loginTime", "وقت تسجيل الدخول"},
                {"lastActivity", "آخر نشاط"},
                {"logoutTime", "وقت تسجيل الخروج"},
                {"action", "الإجراء (EN)"},
                {"actionAr", "الإجراء"},
                {"tableName", "اسم الجدول"},
                {"recordId", "معرف السجل"},
                {"oldValues", "القيم القديمة"},
                {"newValues", "القيم الجديدة"},
                {"timestamp", "الطابع الزمني"}
            };

            return translations.ContainsKey(columnName) ? translations[columnName] : columnName;
        }

        private void LoadData()
        {
            if (!isEditMode || recordData == null) return;

            try
            {
                foreach (var kvp in fieldControls)
                {
                    string columnName = kvp.Key;
                    Control control = kvp.Value;

                    if (recordData.Table.Columns.Contains(columnName))
                    {
                        object value = recordData[columnName];

                        if (value != null && value != DBNull.Value)
                        {
                            SetControlValue(control, value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetControlValue(Control control, object value)
        {
            try
            {
                if (control is Guna2TextBox textBox)
                {
                    textBox.Text = value.ToString();
                }
                else if (control is Guna2CheckBox checkBox)
                {
                    checkBox.Checked = Convert.ToBoolean(value);
                }
                else if (control is Guna2DateTimePicker datePicker)
                {
                    if (DateTime.TryParse(value.ToString(), out DateTime dateValue))
                    {
                        datePicker.Value = dateValue;
                        if (datePicker.ShowCheckBox)
                        {
                            datePicker.Checked = true;
                        }
                    }
                }
                else if (control is Guna2NumericUpDown numericUpDown)
                {
                    if (decimal.TryParse(value.ToString(), out decimal numValue))
                    {
                        numericUpDown.Value = numValue;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but continue with other fields
                Console.WriteLine($"Error setting value for control {control.Name}: {ex.Message}");
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    if (SaveRecord())
                    {
                        this.DialogResult = DialogResult.OK;
                        this.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السجل: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            // Basic validation - check required fields
            foreach (DataRow schemaRow in tableSchema.Rows)
            {
                string columnName = schemaRow["COLUMN_NAME"].ToString();
                bool isNullable = schemaRow["IS_NULLABLE"].ToString() == "YES";

                if (!isNullable && fieldControls.ContainsKey(columnName))
                {
                    Control control = fieldControls[columnName];
                    object value = GetControlValue(control);

                    if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
                    {
                        MessageBox.Show($"الحقل '{GetArabicColumnName(columnName)}' مطلوب", "خطأ في التحقق",
                                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        control.Focus();
                        return false;
                    }
                }
            }

            return true;
        }

        private object GetControlValue(Control control)
        {
            if (control is Guna2TextBox textBox)
            {
                return string.IsNullOrWhiteSpace(textBox.Text) ? null : textBox.Text.Trim();
            }
            else if (control is Guna2CheckBox checkBox)
            {
                return checkBox.Checked;
            }
            else if (control is Guna2DateTimePicker datePicker)
            {
                if (datePicker.ShowCheckBox && !datePicker.Checked)
                    return null;
                return datePicker.Value;
            }
            else if (control is Guna2NumericUpDown numericUpDown)
            {
                return numericUpDown.Value;
            }

            return null;
        }

        private bool SaveRecord()
        {
            try
            {
                List<SqlParameter> parameters = new List<SqlParameter>();
                List<string> columnNames = new List<string>();
                List<string> parameterNames = new List<string>();
                List<string> updateClauses = new List<string>();

                foreach (var kvp in fieldControls)
                {
                    string columnName = kvp.Key;
                    Control control = kvp.Value;

                    // Skip ID column for insert operations
                    if (!isEditMode && columnName.ToLower() == "id")
                        continue;

                    object value = GetControlValue(control);

                    columnNames.Add(columnName);
                    parameterNames.Add($"@{columnName}");
                    updateClauses.Add($"{columnName} = @{columnName}");

                    parameters.Add(new SqlParameter($"@{columnName}", value ?? DBNull.Value));
                }

                string query;
                if (isEditMode)
                {
                    // Update query
                    query = $"UPDATE {tableName} SET {string.Join(", ", updateClauses)} WHERE id = @id";

                    // Add ID parameter for WHERE clause
                    if (recordData != null && recordData.Table.Columns.Contains("id"))
                    {
                        parameters.Add(new SqlParameter("@id", recordData["id"]));
                    }
                }
                else
                {
                    // Insert query
                    query = $"INSERT INTO {tableName} ({string.Join(", ", columnNames)}) VALUES ({string.Join(", ", parameterNames)})";
                }

                int rowsAffected = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters.ToArray());

                return rowsAffected > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
    }
}
