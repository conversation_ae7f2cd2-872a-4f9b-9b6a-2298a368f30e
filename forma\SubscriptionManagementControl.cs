using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class SubscriptionManagementControl : UserControl
    {
        private Panel headerPanel;
        private Panel searchPanel;
        private Panel contentPanel;
        private Panel buttonPanel;

        private Label lblTitle;
        private Label lblResultsCount;

        private Guna2TextBox txtSearch;
        private Guna2ComboBox cmbSubscriptionFilter;
        private Guna2ComboBox cmbStatusFilter;

        private Guna2DataGridView dgvSubscriptions;
        private Guna2Button btnUpdateSubscription;
        private Guna2Button btnRefresh;

        private DataTable subscriptionsData;
        private System.Windows.Forms.Timer searchTimer;

        public SubscriptionManagementControl()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Main control settings
            this.Size = new Size(1200, 800);
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.Dock = DockStyle.Fill;

            // Create panels
            CreatePanels();
            InitializeHeaderControls();
            InitializeSearchControls();
            InitializeButtons();
            InitializeDataGridView();

            this.ResumeLayout(false);
        }

        private void CreatePanels()
        {
            // Header Panel
            headerPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                Padding = new Padding(20, 10, 20, 10)
            };
            this.Controls.Add(headerPanel);

            // Search Panel
            searchPanel = new Panel
            {
                Height = 90,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(250, 252, 253),
                Padding = new Padding(20, 10, 20, 10)
            };
            this.Controls.Add(searchPanel);

            // Button Panel
            buttonPanel = new Panel
            {
                Height = 70,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                Padding = new Padding(20, 10, 20, 10)
            };
            this.Controls.Add(buttonPanel);

            // Content Panel
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Padding = new Padding(20)
            };
            this.Controls.Add(contentPanel);
        }

        private void InitializeCustomComponents()
        {
            // Set right-to-left for Arabic support
            this.RightToLeft = RightToLeft.Yes;
        }

        private void InitializeHeaderControls()
        {
            // Title
            lblTitle = new Label
            {
                Text = "إدارة اشتراكات الصيدليات",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Location = new Point(30, 25),
                AutoSize = true
            };
            headerPanel.Controls.Add(lblTitle);

            // Results Count Label
            lblResultsCount = new Label
            {
                Text = "",
                Font = new Font("Segoe UI", 12F),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(450, 30),
                AutoSize = true
            };
            headerPanel.Controls.Add(lblResultsCount);
        }

        private void InitializeSearchControls()
        {
            // Search TextBox
            txtSearch = new Guna2TextBox
            {
                Location = new Point(30, 30),
                Size = new Size(300, 40),
                PlaceholderText = "البحث بالاسم أو كود الصيدلية...",
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(44, 62, 80)
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            searchPanel.Controls.Add(txtSearch);

            // Subscription Filter
            cmbSubscriptionFilter = new Guna2ComboBox
            {
                Location = new Point(350, 30),
                Size = new Size(200, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(44, 62, 80)
            };
            cmbSubscriptionFilter.Items.AddRange(new string[] { 
                "جميع الاشتراكات", "تجريبي", "أساسي", "مميز", "مؤسسي" 
            });
            cmbSubscriptionFilter.SelectedIndex = 0;
            cmbSubscriptionFilter.SelectedIndexChanged += CmbSubscriptionFilter_SelectedIndexChanged;
            searchPanel.Controls.Add(cmbSubscriptionFilter);

            // Status Filter
            cmbStatusFilter = new Guna2ComboBox
            {
                Location = new Point(570, 30),
                Size = new Size(180, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(44, 62, 80)
            };
            cmbStatusFilter.Items.AddRange(new string[] { 
                "جميع الحالات", "نشط", "منتهي الصلاحية", "معلق", "ملغي" 
            });
            cmbStatusFilter.SelectedIndex = 0;
            cmbStatusFilter.SelectedIndexChanged += CmbStatusFilter_SelectedIndexChanged;
            searchPanel.Controls.Add(cmbStatusFilter);

            // Clear Button
            var btnClear = new Guna2Button
            {
                Location = new Point(770, 30),
                Size = new Size(80, 40),
                Text = "مسح",
                BorderRadius = 8,
                FillColor = Color.FromArgb(149, 165, 166),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnClear.Click += BtnClear_Click;
            searchPanel.Controls.Add(btnClear);
        }

        private void InitializeButtons()
        {
            int buttonY = 15;
            int buttonHeight = 40;
            int buttonWidth = 160;
            int spacing = 15;

            // Update Subscription Button
            btnUpdateSubscription = new Guna2Button
            {
                Location = new Point(30, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = "تحديث الاشتراك",
                BorderRadius = 8,
                FillColor = Color.FromArgb(52, 152, 219),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                Enabled = false
            };
            btnUpdateSubscription.Click += BtnUpdateSubscription_Click;
            buttonPanel.Controls.Add(btnUpdateSubscription);

            // Refresh Button
            btnRefresh = new Guna2Button
            {
                Location = new Point(30 + buttonWidth + spacing, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = "تحديث البيانات",
                BorderRadius = 8,
                FillColor = Color.FromArgb(46, 204, 113),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnRefresh.Click += BtnRefresh_Click;
            buttonPanel.Controls.Add(btnRefresh);
        }

        private void InitializeDataGridView()
        {
            dgvSubscriptions = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.Fixed,
                ColumnHeadersHeight = 40,
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(231, 229, 255),
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                AllowUserToResizeRows = false,
                RowHeadersVisible = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Style headers
            dgvSubscriptions.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvSubscriptions.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSubscriptions.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11F, FontStyle.Bold);
            dgvSubscriptions.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            // Style rows
            dgvSubscriptions.DefaultCellStyle.BackColor = Color.White;
            dgvSubscriptions.DefaultCellStyle.ForeColor = Color.FromArgb(44, 62, 80);
            dgvSubscriptions.DefaultCellStyle.Font = new Font("Segoe UI", 10F);
            dgvSubscriptions.DefaultCellStyle.SelectionBackColor = Color.FromArgb(231, 229, 255);
            dgvSubscriptions.DefaultCellStyle.SelectionForeColor = Color.FromArgb(44, 62, 80);

            // Alternating row colors
            dgvSubscriptions.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            dgvSubscriptions.SelectionChanged += DgvSubscriptions_SelectionChanged;
            dgvSubscriptions.CellDoubleClick += DgvSubscriptions_CellDoubleClick;
            dgvSubscriptions.CellFormatting += DgvSubscriptions_CellFormatting;

            dgvSubscriptions.Margin = new Padding(20);
            contentPanel.Controls.Add(dgvSubscriptions);
        }

        private void LoadData()
        {
            try
            {
                // Test database connection first
                if (!DatabaseConnection.TestPharmacyConnection())
                {
                    MessageBox.Show("خطأ في الاتصال بقاعدة البيانات", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);

                    // Create empty table to prevent black screen
                    subscriptionsData = CreateEmptyDataTable();
                    SetupDataGridViewColumns();
                    dgvSubscriptions.DataSource = subscriptionsData;
                    return;
                }

                // Load subscriptions data
                subscriptionsData = GetSubscriptionsData();

                if (subscriptionsData == null || subscriptionsData.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات صيدليات في قاعدة البيانات", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    subscriptionsData = CreateEmptyDataTable();
                }

                SetupDataGridViewColumns();
                dgvSubscriptions.DataSource = subscriptionsData;

                // Update UI
                UpdateButtonStates();
                UpdateResultsCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Create empty table to prevent black screen
                subscriptionsData = CreateEmptyDataTable();
                SetupDataGridViewColumns();
                dgvSubscriptions.DataSource = subscriptionsData;
                UpdateResultsCount();
            }
        }

        private DataTable CreateEmptyDataTable()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("id", typeof(int));
            dt.Columns.Add("pharmacyCode", typeof(string));
            dt.Columns.Add("pharmacyNameAr", typeof(string));
            dt.Columns.Add("subscriptionStatus", typeof(string));
            dt.Columns.Add("subscriptionStatusAr", typeof(string));
            dt.Columns.Add("isActive", typeof(bool));
            dt.Columns.Add("registrationDate", typeof(DateTime));
            dt.Columns.Add("subscriptionStartDate", typeof(DateTime));
            dt.Columns.Add("subscriptionEndDate", typeof(DateTime));
            dt.Columns.Add("subscriptionDuration", typeof(string));
            dt.Columns.Add("status", typeof(string));
            dt.Columns.Add("statusAr", typeof(string));
            return dt;
        }

        private DataTable GetSubscriptionsData()
        {
            try
            {
                string query = @"
                    SELECT p.id,
                           ISNULL(p.pharmacyCode, '') as pharmacyCode,
                           ISNULL(p.pharmacyNameAr, p.pharmacyName) as pharmacyNameAr,
                           ISNULL(p.subscriptionStatus, 'Trial') as subscriptionStatus,
                           CASE ISNULL(p.subscriptionStatus, 'Trial')
                               WHEN 'Trial' THEN 'تجريبي'
                               WHEN 'Basic' THEN 'أساسي'
                               WHEN 'Premium' THEN 'مميز'
                               WHEN 'Enterprise' THEN 'مؤسسي'
                               ELSE 'غير محدد'
                           END as subscriptionStatusAr,
                           ISNULL(CAST(p.isActive as BIT), 1) as isActive,
                           ISNULL(p.registrationDate, GETDATE()) as registrationDate,
                           ISNULL(p.registrationDate, GETDATE()) as subscriptionStartDate,
                           CASE
                               WHEN p.subscriptionStatus = 'Trial' THEN DATEADD(day, 30, ISNULL(p.registrationDate, GETDATE()))
                               WHEN p.subscriptionStatus = 'Basic' THEN DATEADD(month, 12, ISNULL(p.registrationDate, GETDATE()))
                               WHEN p.subscriptionStatus = 'Premium' THEN DATEADD(month, 12, ISNULL(p.registrationDate, GETDATE()))
                               WHEN p.subscriptionStatus = 'Enterprise' THEN DATEADD(month, 24, ISNULL(p.registrationDate, GETDATE()))
                               ELSE DATEADD(day, 30, ISNULL(p.registrationDate, GETDATE()))
                           END as subscriptionEndDate,
                           CASE
                               WHEN p.subscriptionStatus = 'Trial' THEN '30 يوم'
                               WHEN p.subscriptionStatus = 'Basic' THEN '12 شهر'
                               WHEN p.subscriptionStatus = 'Premium' THEN '12 شهر'
                               WHEN p.subscriptionStatus = 'Enterprise' THEN '24 شهر'
                               ELSE '30 يوم'
                           END as subscriptionDuration,
                           ISNULL(p.status, 'Pending') as status,
                           CASE ISNULL(p.status, 'Pending')
                               WHEN 'Pending' THEN 'في الانتظار'
                               WHEN 'Approved' THEN 'موافق عليه'
                               WHEN 'Rejected' THEN 'مرفوض'
                               WHEN 'Suspended' THEN 'معلق'
                               ELSE 'غير محدد'
                           END as statusAr
                    FROM pharmacies p
                    ORDER BY p.registrationDate DESC";

                return DatabaseConnection.ExecutePharmacyQuery(query);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في جلب بيانات الاشتراكات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                return CreateEmptyDataTable();
            }
        }

        private void SetupDataGridViewColumns()
        {
            dgvSubscriptions.AutoGenerateColumns = false;
            dgvSubscriptions.Columns.Clear();

            // ID Column (Hidden)
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "id",
                Name = "Id",
                Visible = false
            });

            // Pharmacy Code Column
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "pharmacyCode",
                Name = "PharmacyCode",
                HeaderText = "كود الصيدلية",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    ForeColor = Color.FromArgb(52, 152, 219)
                }
            });

            // Pharmacy Name Column
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "pharmacyNameAr",
                Name = "PharmacyName",
                HeaderText = "اسم الصيدلية",
                Width = 200,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            });

            // Subscription Status Column
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "subscriptionStatusAr",
                Name = "SubscriptionStatus",
                HeaderText = "نوع الاشتراك",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold)
                }
            });

            // Status Column
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "statusAr",
                Name = "Status",
                HeaderText = "الحالة",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });

            // Registration Date Column
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "registrationDate",
                Name = "RegistrationDate",
                HeaderText = "تاريخ التسجيل",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "yyyy/MM/dd"
                }
            });

            // Subscription End Date Column
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "subscriptionEndDate",
                Name = "SubscriptionEndDate",
                HeaderText = "انتهاء الاشتراك",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "yyyy/MM/dd"
                }
            });

            // Subscription Duration Column
            dgvSubscriptions.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "subscriptionDuration",
                Name = "SubscriptionDuration",
                HeaderText = "مدة الاشتراك",
                Width = 100,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                    ForeColor = Color.FromArgb(46, 204, 113)
                }
            });

            // Active Status Column
            dgvSubscriptions.Columns.Add(new DataGridViewCheckBoxColumn
            {
                DataPropertyName = "isActive",
                Name = "IsActive",
                HeaderText = "نشط",
                Width = 80,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                }
            });
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = dgvSubscriptions.SelectedRows.Count > 0;
            btnUpdateSubscription.Enabled = hasSelection;
        }

        private void UpdateResultsCount()
        {
            try
            {
                int totalCount = subscriptionsData?.Rows.Count ?? 0;
                int filteredCount = subscriptionsData?.DefaultView.Count ?? 0;

                if (totalCount == filteredCount)
                {
                    lblResultsCount.Text = $"إجمالي: {totalCount} صيدلية";
                }
                else
                {
                    lblResultsCount.Text = $"عرض: {filteredCount} من أصل {totalCount} صيدلية";
                }
            }
            catch
            {
                lblResultsCount.Text = "";
            }
        }

        // Event Handlers
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            // Add a small delay to avoid too frequent filtering while typing
            if (searchTimer != null)
            {
                searchTimer.Stop();
                searchTimer.Dispose();
            }

            searchTimer = new System.Windows.Forms.Timer();
            searchTimer.Interval = 300; // 300ms delay
            searchTimer.Tick += (s, args) =>
            {
                searchTimer.Stop();
                searchTimer.Dispose();
                searchTimer = null;
                ApplyFilters();
            };
            searchTimer.Start();
        }

        private void CmbSubscriptionFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbStatusFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbSubscriptionFilter.SelectedIndex = 0;
            cmbStatusFilter.SelectedIndex = 0;
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void DgvSubscriptions_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void DgvSubscriptions_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnUpdateSubscription_Click(sender, e);
            }
        }

        private void DgvSubscriptions_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (dgvSubscriptions.Columns[e.ColumnIndex].Name == "SubscriptionStatus")
                {
                    string subscriptionType = e.Value?.ToString() ?? "";
                    switch (subscriptionType)
                    {
                        case "تجريبي":
                            e.CellStyle.BackColor = Color.FromArgb(255, 243, 205);
                            e.CellStyle.ForeColor = Color.FromArgb(133, 100, 4);
                            break;
                        case "أساسي":
                            e.CellStyle.BackColor = Color.FromArgb(209, 231, 221);
                            e.CellStyle.ForeColor = Color.FromArgb(21, 87, 36);
                            break;
                        case "مميز":
                            e.CellStyle.BackColor = Color.FromArgb(204, 229, 255);
                            e.CellStyle.ForeColor = Color.FromArgb(12, 84, 96);
                            break;
                        case "مؤسسي":
                            e.CellStyle.BackColor = Color.FromArgb(248, 215, 218);
                            e.CellStyle.ForeColor = Color.FromArgb(114, 28, 36);
                            break;
                    }
                }
                else if (dgvSubscriptions.Columns[e.ColumnIndex].Name == "Status")
                {
                    string status = e.Value?.ToString() ?? "";
                    switch (status)
                    {
                        case "موافق عليه":
                            e.CellStyle.BackColor = Color.FromArgb(209, 231, 221);
                            e.CellStyle.ForeColor = Color.FromArgb(21, 87, 36);
                            break;
                        case "في الانتظار":
                            e.CellStyle.BackColor = Color.FromArgb(255, 243, 205);
                            e.CellStyle.ForeColor = Color.FromArgb(133, 100, 4);
                            break;
                        case "مرفوض":
                        case "معلق":
                            e.CellStyle.BackColor = Color.FromArgb(248, 215, 218);
                            e.CellStyle.ForeColor = Color.FromArgb(114, 28, 36);
                            break;
                    }
                }
                else if (dgvSubscriptions.Columns[e.ColumnIndex].Name == "SubscriptionEndDate")
                {
                    if (DateTime.TryParse(e.Value?.ToString(), out DateTime endDate))
                    {
                        TimeSpan timeLeft = endDate - DateTime.Today;
                        if (timeLeft.TotalDays <= 0)
                        {
                            // Expired
                            e.CellStyle.BackColor = Color.FromArgb(248, 215, 218);
                            e.CellStyle.ForeColor = Color.FromArgb(114, 28, 36);
                        }
                        else if (timeLeft.TotalDays <= 7)
                        {
                            // Expires soon
                            e.CellStyle.BackColor = Color.FromArgb(255, 243, 205);
                            e.CellStyle.ForeColor = Color.FromArgb(133, 100, 4);
                        }
                        else if (timeLeft.TotalDays <= 30)
                        {
                            // Expires within a month
                            e.CellStyle.BackColor = Color.FromArgb(255, 248, 220);
                            e.CellStyle.ForeColor = Color.FromArgb(102, 77, 3);
                        }
                    }
                }
            }
            catch
            {
                // Ignore formatting errors
            }
        }

        private void BtnUpdateSubscription_Click(object sender, EventArgs e)
        {
            if (dgvSubscriptions.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار صيدلية لتحديث اشتراكها", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                int pharmacyId = Convert.ToInt32(dgvSubscriptions.SelectedRows[0].Cells["Id"].Value);
                string pharmacyName = dgvSubscriptions.SelectedRows[0].Cells["PharmacyName"].Value.ToString();
                string currentSubscription = dgvSubscriptions.SelectedRows[0].Cells["SubscriptionStatus"].Value.ToString();

                // Create and show subscription update form
                var updateForm = new UpdateSubscriptionForm(pharmacyId, pharmacyName, currentSubscription);

                if (updateForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData(); // Refresh data after update
                    MessageBox.Show("تم تحديث الاشتراك بنجاح", "نجح",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الاشتراك: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyFilters()
        {
            if (subscriptionsData == null || subscriptionsData.Rows.Count == 0) return;

            try
            {
                string filter = "1=1";

                // Search filter
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    string searchText = txtSearch.Text.Trim().Replace("'", "''");
                    filter += $" AND (pharmacyNameAr LIKE '%{searchText}%' OR pharmacyCode LIKE '%{searchText}%')";
                }

                // Subscription filter
                if (cmbSubscriptionFilter.SelectedIndex > 0 && cmbSubscriptionFilter.SelectedItem != null)
                {
                    string selectedSubscription = cmbSubscriptionFilter.SelectedItem.ToString();
                    string subscriptionValue = "";
                    switch (selectedSubscription)
                    {
                        case "تجريبي": subscriptionValue = "Trial"; break;
                        case "أساسي": subscriptionValue = "Basic"; break;
                        case "مميز": subscriptionValue = "Premium"; break;
                        case "مؤسسي": subscriptionValue = "Enterprise"; break;
                    }
                    if (!string.IsNullOrEmpty(subscriptionValue))
                    {
                        filter += $" AND subscriptionStatus = '{subscriptionValue}'";
                    }
                }

                // Status filter
                if (cmbStatusFilter.SelectedIndex > 0 && cmbStatusFilter.SelectedItem != null)
                {
                    string selectedStatus = cmbStatusFilter.SelectedItem.ToString();
                    string statusValue = "";
                    switch (selectedStatus)
                    {
                        case "نشط": statusValue = "Approved"; break;
                        case "منتهي الصلاحية": statusValue = "Expired"; break;
                        case "معلق": statusValue = "Suspended"; break;
                        case "ملغي": statusValue = "Rejected"; break;
                    }
                    if (!string.IsNullOrEmpty(statusValue))
                    {
                        filter += $" AND status = '{statusValue}'";
                    }
                }

                subscriptionsData.DefaultView.RowFilter = filter;
                UpdateResultsCount();
            }
            catch (Exception ex)
            {
                // Reset filter on error
                try
                {
                    subscriptionsData.DefaultView.RowFilter = "";
                    UpdateResultsCount();
                }
                catch { }
            }
        }
    }
}
