using System;
using System.Drawing;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class UpdateSubscriptionForm : Form
    {
        private int pharmacyId;
        private string pharmacyName;
        private string currentSubscription;

        private Panel contentPanel;
        private Panel buttonPanel;

        private Label lblTitle;
        private Label lblPharmacyName;
        private Label lblCurrentSubscription;
        private Label lblNewSubscription;
        private Label lblDuration;
        private Label lblStartDate;
        private Label lblEndDate;
        private Label lblNotes;

        private Guna2ComboBox cmbNewSubscription;
        private Guna2ComboBox cmbDuration;
        private Guna2DateTimePicker dtpStartDate;
        private Guna2DateTimePicker dtpEndDate;
        private Guna2TextBox txtNotes;

        private Guna2Button btnSave;
        private Guna2Button btnCancel;

        public UpdateSubscriptionForm(int pharmacyId, string pharmacyName, string currentSubscription)
        {
            this.pharmacyId = pharmacyId;
            this.pharmacyName = pharmacyName;
            this.currentSubscription = currentSubscription;

            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form settings
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.ShowInTaskbar = false;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Text = "تحديث الاشتراك";
            this.RightToLeft = RightToLeft.Yes;

            CreatePanels();
            InitializeControls();
            InitializeButtons();

            this.ResumeLayout(false);
        }

        private void CreatePanels()
        {
            // Content Panel
            contentPanel = new Panel
            {
                Location = new Point(20, 20),
                Size = new Size(540, 380),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(contentPanel);

            // Button Panel
            buttonPanel = new Panel
            {
                Location = new Point(20, 420),
                Size = new Size(540, 60),
                BackColor = Color.Transparent
            };
            this.Controls.Add(buttonPanel);
        }

        private void InitializeCustomComponents()
        {
            // Set right-to-left for Arabic support
            this.RightToLeft = RightToLeft.Yes;
        }

        private void InitializeControls()
        {
            int startY = 20;
            int labelHeight = 25;
            int controlHeight = 40;
            int spacing = 20;
            int leftX = 30;
            int controlWidth = 480;

            // Title
            lblTitle = new Label
            {
                Text = "تحديث اشتراك الصيدلية",
                Font = new Font("Segoe UI", 16F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Location = new Point(leftX, startY),
                Size = new Size(controlWidth, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            contentPanel.Controls.Add(lblTitle);
            startY += 50;

            // Pharmacy Name
            lblPharmacyName = CreateLabel("اسم الصيدلية:", leftX, startY);
            contentPanel.Controls.Add(lblPharmacyName);
            startY += labelHeight + 5;

            var lblPharmacyNameValue = new Label
            {
                Text = pharmacyName,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(leftX, startY),
                Size = new Size(controlWidth, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            contentPanel.Controls.Add(lblPharmacyNameValue);
            startY += 35;

            // Current Subscription
            lblCurrentSubscription = CreateLabel("الاشتراك الحالي:", leftX, startY);
            contentPanel.Controls.Add(lblCurrentSubscription);
            startY += labelHeight + 5;

            var lblCurrentSubscriptionValue = new Label
            {
                Text = currentSubscription,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(leftX, startY),
                Size = new Size(controlWidth, 25),
                TextAlign = ContentAlignment.MiddleRight
            };
            contentPanel.Controls.Add(lblCurrentSubscriptionValue);
            startY += 35;

            // New Subscription
            lblNewSubscription = CreateLabel("الاشتراك الجديد:", leftX, startY);
            contentPanel.Controls.Add(lblNewSubscription);
            startY += labelHeight;

            cmbNewSubscription = CreateComboBox(leftX, startY, controlWidth);
            cmbNewSubscription.Items.AddRange(new string[] { "تجريبي", "أساسي", "مميز", "مؤسسي" });
            cmbNewSubscription.SelectedIndexChanged += CmbNewSubscription_SelectedIndexChanged;
            contentPanel.Controls.Add(cmbNewSubscription);
            startY += controlHeight + spacing;

            // Duration
            lblDuration = CreateLabel("مدة الاشتراك:", leftX, startY);
            contentPanel.Controls.Add(lblDuration);
            startY += labelHeight;

            cmbDuration = CreateComboBox(leftX, startY, controlWidth);
            cmbDuration.Items.AddRange(new string[] { "30 يوم", "3 أشهر", "6 أشهر", "12 شهر", "24 شهر" });
            cmbDuration.SelectedIndexChanged += CmbDuration_SelectedIndexChanged;
            contentPanel.Controls.Add(cmbDuration);
            startY += controlHeight + spacing;

            // Start Date
            lblStartDate = CreateLabel("تاريخ البداية:", leftX, startY);
            contentPanel.Controls.Add(lblStartDate);
            startY += labelHeight;

            dtpStartDate = CreateDateTimePicker(leftX, startY, controlWidth / 2 - 10);
            dtpStartDate.Value = DateTime.Today;
            dtpStartDate.ValueChanged += DtpStartDate_ValueChanged;
            contentPanel.Controls.Add(dtpStartDate);

            // End Date
            lblEndDate = CreateLabel("تاريخ الانتهاء:", leftX + controlWidth / 2 + 10, startY - labelHeight);
            contentPanel.Controls.Add(lblEndDate);

            dtpEndDate = CreateDateTimePicker(leftX + controlWidth / 2 + 10, startY, controlWidth / 2 - 10);
            dtpEndDate.Value = DateTime.Today.AddMonths(1);
            contentPanel.Controls.Add(dtpEndDate);
            startY += controlHeight + spacing;

            // Notes
            lblNotes = CreateLabel("ملاحظات:", leftX, startY);
            contentPanel.Controls.Add(lblNotes);
            startY += labelHeight;

            txtNotes = CreateTextBox(leftX, startY, controlWidth, "ملاحظات إضافية (اختياري)");
            txtNotes.Multiline = true;
            txtNotes.Size = new Size(controlWidth, 60);
            contentPanel.Controls.Add(txtNotes);
        }

        private void InitializeButtons()
        {
            // Save Button
            btnSave = new Guna2Button
            {
                Location = new Point(30, 15),
                Size = new Size(120, 40),
                Text = "حفظ التحديث",
                BorderRadius = 8,
                FillColor = Color.FromArgb(46, 204, 113),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnSave.Click += BtnSave_Click;
            buttonPanel.Controls.Add(btnSave);

            // Cancel Button
            btnCancel = new Guna2Button
            {
                Location = new Point(170, 15),
                Size = new Size(120, 40),
                Text = "إلغاء",
                BorderRadius = 8,
                FillColor = Color.FromArgb(231, 76, 60),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold)
            };
            btnCancel.Click += BtnCancel_Click;
            buttonPanel.Controls.Add(btnCancel);
        }

        private Label CreateLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleRight
            };
        }

        private Guna2ComboBox CreateComboBox(int x, int y, int width)
        {
            return new Guna2ComboBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                ForeColor = Color.FromArgb(44, 62, 80)
            };
        }

        private Guna2DateTimePicker CreateDateTimePicker(int x, int y, int width)
        {
            return new Guna2DateTimePicker
            {
                Location = new Point(x, y),
                Size = new Size(width, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                Format = DateTimePickerFormat.Short,
                ForeColor = Color.FromArgb(44, 62, 80)
            };
        }

        private Guna2TextBox CreateTextBox(int x, int y, int width, string placeholder)
        {
            return new Guna2TextBox
            {
                Location = new Point(x, y),
                Size = new Size(width, 40),
                PlaceholderText = placeholder,
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11F),
                ForeColor = Color.FromArgb(44, 62, 80)
            };
        }

        private void LoadData()
        {
            // Set default values based on current subscription
            switch (currentSubscription)
            {
                case "تجريبي":
                    cmbNewSubscription.SelectedIndex = 0;
                    cmbDuration.SelectedIndex = 0; // 30 days
                    break;
                case "أساسي":
                    cmbNewSubscription.SelectedIndex = 1;
                    cmbDuration.SelectedIndex = 3; // 12 months
                    break;
                case "مميز":
                    cmbNewSubscription.SelectedIndex = 2;
                    cmbDuration.SelectedIndex = 3; // 12 months
                    break;
                case "مؤسسي":
                    cmbNewSubscription.SelectedIndex = 3;
                    cmbDuration.SelectedIndex = 4; // 24 months
                    break;
                default:
                    cmbNewSubscription.SelectedIndex = 0;
                    cmbDuration.SelectedIndex = 0;
                    break;
            }

            UpdateEndDate();
        }

        private void CmbNewSubscription_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Auto-select appropriate duration based on subscription type
            switch (cmbNewSubscription.SelectedIndex)
            {
                case 0: // Trial
                    cmbDuration.SelectedIndex = 0; // 30 days
                    break;
                case 1: // Basic
                    cmbDuration.SelectedIndex = 3; // 12 months
                    break;
                case 2: // Premium
                    cmbDuration.SelectedIndex = 3; // 12 months
                    break;
                case 3: // Enterprise
                    cmbDuration.SelectedIndex = 4; // 24 months
                    break;
            }
            UpdateEndDate();
        }

        private void CmbDuration_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateEndDate();
        }

        private void DtpStartDate_ValueChanged(object sender, EventArgs e)
        {
            UpdateEndDate();
        }

        private void UpdateEndDate()
        {
            if (cmbDuration.SelectedIndex >= 0)
            {
                DateTime startDate = dtpStartDate.Value;
                DateTime endDate = startDate;

                switch (cmbDuration.SelectedIndex)
                {
                    case 0: // 30 days
                        endDate = startDate.AddDays(30);
                        break;
                    case 1: // 3 months
                        endDate = startDate.AddMonths(3);
                        break;
                    case 2: // 6 months
                        endDate = startDate.AddMonths(6);
                        break;
                    case 3: // 12 months
                        endDate = startDate.AddMonths(12);
                        break;
                    case 4: // 24 months
                        endDate = startDate.AddMonths(24);
                        break;
                }

                dtpEndDate.Value = endDate;
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                string newSubscriptionType = GetSubscriptionTypeValue();
                DateTime startDate = dtpStartDate.Value;
                DateTime endDate = dtpEndDate.Value;
                string notes = txtNotes.Text.Trim();

                bool success = UpdatePharmacySubscription(pharmacyId, newSubscriptionType, startDate, endDate, notes);

                if (success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في تحديث الاشتراك", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التحديث: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            if (cmbNewSubscription.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار نوع الاشتراك الجديد", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbNewSubscription.Focus();
                return false;
            }

            if (cmbDuration.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار مدة الاشتراك", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbDuration.Focus();
                return false;
            }

            if (dtpEndDate.Value <= dtpStartDate.Value)
            {
                MessageBox.Show("تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpEndDate.Focus();
                return false;
            }

            return true;
        }

        private string GetSubscriptionTypeValue()
        {
            switch (cmbNewSubscription.SelectedIndex)
            {
                case 0: return "Trial";
                case 1: return "Basic";
                case 2: return "Premium";
                case 3: return "Enterprise";
                default: return "Trial";
            }
        }

        private bool UpdatePharmacySubscription(int pharmacyId, string subscriptionType, DateTime startDate, DateTime endDate, string notes)
        {
            try
            {
                string query = @"
                    UPDATE pharmacies
                    SET subscriptionStatus = @subscriptionStatus,
                        lastActivityDate = GETDATE()
                    WHERE id = @id";

                var parameters = new System.Data.SqlClient.SqlParameter[]
                {
                    new System.Data.SqlClient.SqlParameter("@id", pharmacyId),
                    new System.Data.SqlClient.SqlParameter("@subscriptionStatus", subscriptionType)
                };

                int result = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);

                if (result > 0)
                {
                    // Log the subscription update activity
                    string subscriptionNameAr = GetSubscriptionNameAr(subscriptionType);
                    AdminUserManager.LogActivity(
                        AdminUserManager.CurrentUser.Id,
                        "UPDATE_SUBSCRIPTION",
                        "تحديث اشتراك",
                        "Pharmacy",
                        pharmacyId,
                        pharmacyName,
                        $"Updated subscription to {subscriptionType} from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}",
                        $"تم تحديث الاشتراك إلى {subscriptionNameAr} من {startDate:yyyy/MM/dd} إلى {endDate:yyyy/MM/dd}"
                    );

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الاشتراك: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        private string GetSubscriptionNameAr(string subscriptionType)
        {
            switch (subscriptionType)
            {
                case "Trial": return "تجريبي";
                case "Basic": return "أساسي";
                case "Premium": return "مميز";
                case "Enterprise": return "مؤسسي";
                default: return "غير محدد";
            }
        }
    }
}
