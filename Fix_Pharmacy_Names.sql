-- إصلاح أسماء الصيدليات في قاعدة البيانات
USE UnifiedPharmacy;

-- تحديث أسماء الصيدليات بالعربية
UPDATE pharmacies SET pharmacyNameAr = N'صيدلية النور' WHERE id = 1;
UPDATE pharmacies SET pharmacyNameAr = N'صيدلية الشفاء' WHERE id = 2;
UPDATE pharmacies SET pharmacyNameAr = N'صيدلية الحياة' WHERE id = 3;
UPDATE pharmacies SET pharmacyNameAr = N'صيدلية الأمل' WHERE id = 4;
UPDATE pharmacies SET pharmacyNameAr = N'صيدلية السلام' WHERE id = 5;
UPDATE pharmacies SET pharmacyNameAr = N'صيدلية الرحمة' WHERE id = 6;
UPDATE pharmacies SET pharmacyNameAr = N'صيدلية الخير' WHERE id = 7;

-- تحديث أسماء المستخدمين بالعربية
UPDATE users SET name = N'أحمد محمد' WHERE id = 1;
UPDATE users SET name = N'سارة أحمد' WHERE id = 2;
UPDATE users SET name = N'محمد علي' WHERE id = 3;
UPDATE users SET name = N'فاطمة حسن' WHERE id = 4;
UPDATE users SET name = N'علي محمود' WHERE id = 9;
UPDATE users SET name = N'نور الدين' WHERE id = 10;
UPDATE users SET name = N'خالد عبدالله' WHERE id = 11;
UPDATE users SET name = N'أبو نزار' WHERE id = 12;

-- عرض النتائج
SELECT u.id, u.name, u.username, u.userRole, p.pharmacyCode, p.pharmacyNameAr 
FROM users u 
LEFT JOIN pharmacies p ON u.pharmacyId = p.id 
ORDER BY u.id;
