using System;
using System.Data;
using System.Windows.Forms;

namespace admino
{
    public static class QuickTestUsers
    {
        public static void TestAll()
        {
            try
            {
                MessageBox.Show("بدء اختبار البيانات...", "اختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Test connection
                bool connected = DatabaseConnection.TestPharmacyConnection();
                MessageBox.Show($"اختبار الاتصال: {(connected ? "نجح" : "فشل")}", "نتيجة الاتصال", 
                              MessageBoxButtons.OK, connected ? MessageBoxIcon.Information : MessageBoxIcon.Error);

                if (!connected) return;

                // Test users data
                DataTable users = PharmacyUserManager.GetAllPharmacyUsers();
                MessageBox.Show($"عدد المستخدمين: {users?.Rows.Count ?? 0}", "بيانات المستخدمين", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Test roles data
                DataTable roles = PharmacyUserManager.GetAllUserRoles();
                MessageBox.Show($"عدد الأدوار: {roles?.Rows.Count ?? 0}", "بيانات الأدوار", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);

                MessageBox.Show("انتهى الاختبار بنجاح!", "مكتمل", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاختبار: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
