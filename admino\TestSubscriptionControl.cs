using System;
using System.Windows.Forms;
using admino.forma;

namespace admino
{
    public partial class TestSubscriptionForm : Form
    {
        private SimpleSubscriptionControl subscriptionControl;

        public TestSubscriptionForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.subscriptionControl = new SimpleSubscriptionControl();
            this.SuspendLayout();
            
            // 
            // subscriptionControl
            // 
            this.subscriptionControl.Dock = DockStyle.Fill;
            this.subscriptionControl.Location = new System.Drawing.Point(0, 0);
            this.subscriptionControl.Name = "subscriptionControl";
            this.subscriptionControl.Size = new System.Drawing.Size(800, 600);
            this.subscriptionControl.TabIndex = 0;
            
            // 
            // TestSubscriptionForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Controls.Add(this.subscriptionControl);
            this.Name = "TestSubscriptionForm";
            this.Text = "اختبار صفحة إدارة الاشتراكات";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.ResumeLayout(false);
        }
    }

    // Simple test program
    public class TestSubscriptionProgram
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                Application.Run(new TestSubscriptionForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التطبيق: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
