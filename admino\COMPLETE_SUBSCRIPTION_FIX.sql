-- =============================================
-- إصلاح شامل لنظام إدارة الاشتراكات
-- Complete Fix for Subscription Management System
-- =============================================

USE UnifiedPharmacy;
GO

PRINT '🔧 بدء الإصلاح الشامل لنظام إدارة الاشتراكات...';
PRINT '================================================';

-- 1. حذف البيانات التجريبية
PRINT '1️⃣ حذف البيانات التجريبية...';

DELETE FROM pharmacies 
WHERE pharmacyNameAr IN (
    'صيدلية النور',
    'صيدلية الشفاء', 
    'صيدلية الحياة',
    'صيدلية الأمل',
    'صيدلية السلام'
) 
OR pharmacyName IN (
    'Al Noor Pharmacy',
    'Al Shifa Pharmacy',
    'Al Hayat Pharmacy',
    'Al Amal Pharmacy',
    'Al Salam Pharmacy'
)
OR licenseNumber IN (
    'LIC001', 'LIC002', 'LIC003', 'LIC004', 'LIC005'
)
OR pharmacyCode IN (
    'PHM001', 'PHM002', 'PHM003', 'PHM004', 'PHM005'
);

PRINT '✅ تم حذف البيانات التجريبية';

-- 2. إضافة الأعمدة المفقودة
PRINT '2️⃣ التحقق من الأعمدة المطلوبة...';

-- إضافة عمود subscriptionStartDate
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStartDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionStartDate DATETIME2 DEFAULT GETDATE();
    PRINT '✅ تم إضافة عمود subscriptionStartDate';
END

-- إضافة عمود subscriptionEndDate
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionEndDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionEndDate DATETIME2;
    PRINT '✅ تم إضافة عمود subscriptionEndDate';
END

-- إضافة عمود pharmacyCode
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'pharmacyCode')
BEGIN
    ALTER TABLE pharmacies ADD pharmacyCode NVARCHAR(20);
    PRINT '✅ تم إضافة عمود pharmacyCode';
END

-- 3. تحديث البيانات الموجودة
PRINT '3️⃣ تحديث البيانات الموجودة...';

-- تحديث أكواد الصيدليات
UPDATE pharmacies 
SET pharmacyCode = 'PHM' + RIGHT('000' + CAST(id AS VARCHAR(3)), 3)
WHERE pharmacyCode IS NULL OR pharmacyCode = '';

-- تحديث تواريخ الاشتراك
UPDATE pharmacies 
SET subscriptionStartDate = ISNULL(subscriptionStartDate, registrationDate),
    subscriptionEndDate = CASE 
        WHEN subscriptionEndDate IS NULL THEN
            CASE 
                WHEN ISNULL(subscriptionStatus, 'Trial') = 'Trial' THEN DATEADD(DAY, 30, ISNULL(subscriptionStartDate, registrationDate))
                WHEN ISNULL(subscriptionStatus, 'Trial') = 'Basic' THEN DATEADD(MONTH, 12, ISNULL(subscriptionStartDate, registrationDate))
                WHEN ISNULL(subscriptionStatus, 'Trial') = 'Premium' THEN DATEADD(MONTH, 12, ISNULL(subscriptionStartDate, registrationDate))
                WHEN ISNULL(subscriptionStatus, 'Trial') = 'Enterprise' THEN DATEADD(MONTH, 24, ISNULL(subscriptionStartDate, registrationDate))
                ELSE DATEADD(MONTH, 1, ISNULL(subscriptionStartDate, registrationDate))
            END
        ELSE subscriptionEndDate
    END
WHERE subscriptionStartDate IS NULL OR subscriptionEndDate IS NULL;

PRINT '✅ تم تحديث البيانات الموجودة';

-- 4. عرض النتائج
PRINT '4️⃣ عرض النتائج النهائية...';

DECLARE @PharmacyCount INT;
SELECT @PharmacyCount = COUNT(*) FROM pharmacies;
PRINT '📊 عدد الصيدليات في النظام: ' + CAST(@PharmacyCount AS VARCHAR(10));

IF @PharmacyCount > 0
BEGIN
    PRINT '📋 قائمة الصيدليات الموجودة:';
    SELECT 
        id as 'الرقم',
        pharmacyCode as 'كود الصيدلية',
        pharmacyNameAr as 'اسم الصيدلية',
        subscriptionStatus as 'نوع الاشتراك',
        FORMAT(subscriptionStartDate, 'yyyy/MM/dd') as 'تاريخ البداية',
        FORMAT(subscriptionEndDate, 'yyyy/MM/dd') as 'تاريخ الانتهاء',
        CASE 
            WHEN subscriptionEndDate > GETDATE() THEN 'نشط'
            ELSE 'منتهي'
        END as 'حالة الاشتراك',
        status as 'حالة الصيدلية'
    FROM pharmacies
    ORDER BY registrationDate DESC;
END
ELSE
BEGIN
    PRINT '⚠️ لا توجد صيدليات مسجلة في النظام';
END

PRINT '================================================';
PRINT '🎉 تم الانتهاء من الإصلاح الشامل بنجاح!';
PRINT '================================================';
