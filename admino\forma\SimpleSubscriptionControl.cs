using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class SimpleSubscriptionControl : UserControl
    {
        private DataGridView dgvSubscriptions;
        private Label lblTitle;
        private Button btnRefresh;
        private Button btnUpdateSubscription;
        private Button btnViewDetails;
        private TextBox txtSearch;
        private Label lblSearch;
        private ComboBox cmbFilter;
        private Label lblFilter;
        private DataTable originalData;
        private DataTable subscriptionsData;

        public SimpleSubscriptionControl()
        {
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Main control settings
            this.Size = new Size(1200, 800);
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.Dock = DockStyle.Fill;

            // Title
            lblTitle = new Label
            {
                Text = "إدارة اشتراكات الصيدليات",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Location = new Point(30, 20),
                Size = new Size(400, 40),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblTitle);

            // Refresh Button
            btnRefresh = new Button
            {
                Text = "تحديث",
                Location = new Point(30, 70),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnRefresh.Click += BtnRefresh_Click;
            this.Controls.Add(btnRefresh);

            // Update Subscription Button
            btnUpdateSubscription = new Button
            {
                Text = "تحديث الاشتراك",
                Location = new Point(150, 70),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(241, 196, 15),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnUpdateSubscription.Click += BtnUpdateSubscription_Click;
            this.Controls.Add(btnUpdateSubscription);

            // View Details Button
            btnViewDetails = new Button
            {
                Text = "عرض التفاصيل",
                Location = new Point(290, 70),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnViewDetails.Click += BtnViewDetails_Click;
            this.Controls.Add(btnViewDetails);

            // Search Label
            lblSearch = new Label
            {
                Text = "البحث:",
                Location = new Point(430, 75),
                Size = new Size(50, 25),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblSearch);

            // Search TextBox
            txtSearch = new TextBox
            {
                Location = new Point(490, 72),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 10F),
                RightToLeft = RightToLeft.Yes
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            this.Controls.Add(txtSearch);

            // Filter Label
            lblFilter = new Label
            {
                Text = "الفلتر:",
                Location = new Point(710, 75),
                Size = new Size(50, 25),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                TextAlign = ContentAlignment.MiddleRight
            };
            this.Controls.Add(lblFilter);

            // Filter ComboBox
            cmbFilter = new ComboBox
            {
                Location = new Point(770, 72),
                Size = new Size(150, 30),
                Font = new Font("Segoe UI", 10F),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            cmbFilter.Items.AddRange(new string[] {
                "جميع الصيدليات",
                "الاشتراكات النشطة",
                "الاشتراكات المنتهية",
                "التجريبي",
                "الأساسي",
                "المميز",
                "المؤسسي"
            });
            cmbFilter.SelectedIndex = 0;
            cmbFilter.SelectedIndexChanged += CmbFilter_SelectedIndexChanged;
            this.Controls.Add(cmbFilter);

            // DataGridView
            dgvSubscriptions = new DataGridView
            {
                Location = new Point(30, 120),
                Size = new Size(1140, 620),
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RowHeadersVisible = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Style headers
            dgvSubscriptions.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvSubscriptions.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvSubscriptions.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11F, FontStyle.Bold);

            // Style cells for Arabic text
            dgvSubscriptions.DefaultCellStyle.Font = new Font("Segoe UI", 10F);
            dgvSubscriptions.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dgvSubscriptions.RightToLeft = RightToLeft.Yes;

            this.Controls.Add(dgvSubscriptions);

            this.ResumeLayout(false);
        }

        private void LoadData()
        {
            try
            {
                // Get data from database
                originalData = GetSubscriptionsData();

                if (originalData != null && originalData.Rows.Count > 0)
                {
                    subscriptionsData = originalData.Copy();
                    dgvSubscriptions.DataSource = subscriptionsData;
                    SetupColumns();
                }
                else
                {
                    // Create empty table with sample data
                    originalData = CreateSampleData();
                    subscriptionsData = originalData.Copy();
                    dgvSubscriptions.DataSource = subscriptionsData;
                    SetupColumns();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Show sample data
                originalData = CreateSampleData();
                subscriptionsData = originalData.Copy();
                dgvSubscriptions.DataSource = subscriptionsData;
                SetupColumns();
            }
        }

        private DataTable GetSubscriptionsData()
        {
            try
            {
                string query = @"
                    SELECT p.id,
                           ISNULL(p.pharmacyCode, '') as pharmacyCode,
                           ISNULL(p.pharmacyNameAr, p.pharmacyName) as pharmacyNameAr,
                           ISNULL(p.subscriptionStatus, 'Trial') as subscriptionStatus,
                           CASE ISNULL(p.subscriptionStatus, 'Trial')
                               WHEN 'Trial' THEN 'تجريبي'
                               WHEN 'Basic' THEN 'أساسي'
                               WHEN 'Premium' THEN 'مميز'
                               WHEN 'Enterprise' THEN 'مؤسسي'
                               ELSE 'غير محدد'
                           END as subscriptionStatusAr,
                           ISNULL(p.registrationDate, GETDATE()) as registrationDate,
                           ISNULL(p.subscriptionEndDate, DATEADD(MONTH, 1, GETDATE())) as subscriptionEndDate,
                           CASE
                               WHEN ISNULL(p.subscriptionEndDate, DATEADD(MONTH, 1, GETDATE())) > GETDATE() THEN 'نشط'
                               ELSE 'منتهي'
                           END as subscriptionStatusText,
                           ISNULL(p.status, 'Pending') as status
                    FROM pharmacies p
                    ORDER BY p.registrationDate DESC";

                return DatabaseConnection.ExecutePharmacyQuery(query);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        private DataTable CreateSampleData()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("id", typeof(int));
            dt.Columns.Add("pharmacyCode", typeof(string));
            dt.Columns.Add("pharmacyNameAr", typeof(string));
            dt.Columns.Add("subscriptionStatus", typeof(string));
            dt.Columns.Add("subscriptionStatusAr", typeof(string));
            dt.Columns.Add("registrationDate", typeof(DateTime));
            dt.Columns.Add("subscriptionEndDate", typeof(DateTime));
            dt.Columns.Add("subscriptionStatusText", typeof(string));
            dt.Columns.Add("status", typeof(string));

            // Add sample rows
            dt.Rows.Add(1, "PHM001", "صيدلية النور", "Trial", "تجريبي", DateTime.Now.AddDays(-10), DateTime.Now.AddDays(20), "نشط", "Approved");
            dt.Rows.Add(2, "PHM002", "صيدلية الشفاء", "Basic", "أساسي", DateTime.Now.AddDays(-20), DateTime.Now.AddDays(340), "نشط", "Approved");
            dt.Rows.Add(3, "PHM003", "صيدلية الحياة", "Premium", "مميز", DateTime.Now.AddDays(-30), DateTime.Now.AddDays(-5), "منتهي", "Pending");

            return dt;
        }

        private void SetupColumns()
        {
            if (dgvSubscriptions.Columns.Count > 0)
            {
                dgvSubscriptions.Columns["id"].Visible = false;
                dgvSubscriptions.Columns["subscriptionStatus"].Visible = false; // Hide English status

                if (dgvSubscriptions.Columns.Contains("pharmacyCode"))
                {
                    dgvSubscriptions.Columns["pharmacyCode"].HeaderText = "كود الصيدلية";
                    dgvSubscriptions.Columns["pharmacyCode"].Width = 100;
                }

                if (dgvSubscriptions.Columns.Contains("pharmacyNameAr"))
                {
                    dgvSubscriptions.Columns["pharmacyNameAr"].HeaderText = "اسم الصيدلية";
                    dgvSubscriptions.Columns["pharmacyNameAr"].Width = 200;
                }

                if (dgvSubscriptions.Columns.Contains("subscriptionStatusAr"))
                {
                    dgvSubscriptions.Columns["subscriptionStatusAr"].HeaderText = "نوع الاشتراك";
                    dgvSubscriptions.Columns["subscriptionStatusAr"].Width = 120;
                }

                if (dgvSubscriptions.Columns.Contains("subscriptionStatusText"))
                {
                    dgvSubscriptions.Columns["subscriptionStatusText"].HeaderText = "حالة الاشتراك";
                    dgvSubscriptions.Columns["subscriptionStatusText"].Width = 100;
                }

                if (dgvSubscriptions.Columns.Contains("registrationDate"))
                {
                    dgvSubscriptions.Columns["registrationDate"].HeaderText = "تاريخ التسجيل";
                    dgvSubscriptions.Columns["registrationDate"].Width = 120;
                    dgvSubscriptions.Columns["registrationDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
                }

                if (dgvSubscriptions.Columns.Contains("subscriptionEndDate"))
                {
                    dgvSubscriptions.Columns["subscriptionEndDate"].HeaderText = "تاريخ انتهاء الاشتراك";
                    dgvSubscriptions.Columns["subscriptionEndDate"].Width = 150;
                    dgvSubscriptions.Columns["subscriptionEndDate"].DefaultCellStyle.Format = "yyyy/MM/dd";
                }

                if (dgvSubscriptions.Columns.Contains("status"))
                {
                    dgvSubscriptions.Columns["status"].HeaderText = "حالة الموافقة";
                    dgvSubscriptions.Columns["status"].Width = 100;
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void BtnUpdateSubscription_Click(object sender, EventArgs e)
        {
            if (dgvSubscriptions.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار صيدلية لتحديث اشتراكها", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedRow = dgvSubscriptions.SelectedRows[0];
            int pharmacyId = Convert.ToInt32(selectedRow.Cells["id"].Value);
            string pharmacyName = selectedRow.Cells["pharmacyNameAr"].Value?.ToString() ?? "";
            string currentStatus = selectedRow.Cells["subscriptionStatus"].Value?.ToString() ?? "";

            // Show update subscription form
            var updateForm = new UpdateSubscriptionForm(pharmacyId, pharmacyName, currentStatus);
            if (updateForm.ShowDialog() == DialogResult.OK)
            {
                LoadData(); // Refresh data after update
            }
        }

        private void BtnViewDetails_Click(object sender, EventArgs e)
        {
            if (dgvSubscriptions.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار صيدلية لعرض تفاصيلها", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedRow = dgvSubscriptions.SelectedRows[0];
            int pharmacyId = Convert.ToInt32(selectedRow.Cells["id"].Value);
            string pharmacyName = selectedRow.Cells["pharmacyNameAr"].Value?.ToString() ?? "";
            string subscriptionStatus = selectedRow.Cells["subscriptionStatusAr"].Value?.ToString() ?? "";
            string status = selectedRow.Cells["status"].Value?.ToString() ?? "";
            DateTime registrationDate = Convert.ToDateTime(selectedRow.Cells["registrationDate"].Value);

            string details = $"تفاصيل الصيدلية:\n\n" +
                           $"الاسم: {pharmacyName}\n" +
                           $"نوع الاشتراك: {subscriptionStatus}\n" +
                           $"الحالة: {status}\n" +
                           $"تاريخ التسجيل: {registrationDate:yyyy/MM/dd}";

            MessageBox.Show(details, "تفاصيل الصيدلية", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void CmbFilter_SelectedIndexChanged(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            if (originalData == null) return;

            try
            {
                DataTable filteredData = originalData.Copy();
                filteredData.Clear();

                string searchText = txtSearch.Text.Trim().ToLower();
                string filterType = cmbFilter.SelectedItem?.ToString() ?? "جميع الصيدليات";

                foreach (DataRow row in originalData.Rows)
                {
                    bool matchesSearch = true;
                    bool matchesFilter = true;

                    // Apply search filter
                    if (!string.IsNullOrEmpty(searchText))
                    {
                        string pharmacyName = row["pharmacyNameAr"]?.ToString()?.ToLower() ?? "";
                        string pharmacyCode = row["pharmacyCode"]?.ToString()?.ToLower() ?? "";

                        matchesSearch = pharmacyName.Contains(searchText) ||
                                      pharmacyCode.Contains(searchText);
                    }

                    // Apply category filter
                    if (filterType != "جميع الصيدليات")
                    {
                        DateTime endDate = Convert.ToDateTime(row["subscriptionEndDate"]);
                        string subscriptionStatusAr = row["subscriptionStatusAr"]?.ToString() ?? "";

                        switch (filterType)
                        {
                            case "الاشتراكات النشطة":
                                matchesFilter = endDate > DateTime.Now;
                                break;
                            case "الاشتراكات المنتهية":
                                matchesFilter = endDate <= DateTime.Now;
                                break;
                            case "التجريبي":
                                matchesFilter = subscriptionStatusAr == "تجريبي";
                                break;
                            case "الأساسي":
                                matchesFilter = subscriptionStatusAr == "أساسي";
                                break;
                            case "المميز":
                                matchesFilter = subscriptionStatusAr == "مميز";
                                break;
                            case "المؤسسي":
                                matchesFilter = subscriptionStatusAr == "مؤسسي";
                                break;
                        }
                    }

                    if (matchesSearch && matchesFilter)
                    {
                        filteredData.ImportRow(row);
                    }
                }

                subscriptionsData = filteredData;
                dgvSubscriptions.DataSource = subscriptionsData;
                SetupColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
