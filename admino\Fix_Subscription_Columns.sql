-- =============================================
-- إصلاح أعمدة الاشتراك في جدول الصيدليات
-- Fix Subscription Columns in Pharmacies Table
-- =============================================

USE UnifiedPharmacy;
GO

PRINT 'بدء إصلاح أعمدة الاشتراك...';

-- التحقق من وجود عمود subscriptionStartDate وإضافته إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStartDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionStartDate DATETIME2 DEFAULT GETDATE();
    PRINT 'تم إضافة عمود subscriptionStartDate';
E<PERSON>
ELSE
BEGIN
    PRINT 'عمود subscriptionStartDate موجود مسبقاً';
END

-- التحقق من وجود عمود subscriptionEndDate وإضافته إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionEndDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionEndDate DATETIME2;
    PRINT 'تم إضافة عمود subscriptionEndDate';
END
ELSE
BEGIN
    PRINT 'عمود subscriptionEndDate موجود مسبقاً';
END

-- التحقق من وجود عمود pharmacyCode وإضافته إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'pharmacyCode')
BEGIN
    ALTER TABLE pharmacies ADD pharmacyCode NVARCHAR(20);
    PRINT 'تم إضافة عمود pharmacyCode';
    
    -- تحديث الصيدليات الموجودة بأكواد تلقائية
    UPDATE pharmacies 
    SET pharmacyCode = 'PHM' + RIGHT('000' + CAST(id AS VARCHAR(3)), 3)
    WHERE pharmacyCode IS NULL;
    
    PRINT 'تم تحديث أكواد الصيدليات الموجودة';
END
ELSE
BEGIN
    PRINT 'عمود pharmacyCode موجود مسبقاً';
END

-- تحديث تواريخ الاشتراك للصيدليات التي لا تحتوي على تواريخ
UPDATE pharmacies 
SET subscriptionStartDate = ISNULL(subscriptionStartDate, registrationDate),
    subscriptionEndDate = CASE 
        WHEN subscriptionEndDate IS NULL THEN
            CASE 
                WHEN subscriptionStatus = 'Trial' THEN DATEADD(DAY, 30, ISNULL(subscriptionStartDate, registrationDate))
                WHEN subscriptionStatus = 'Basic' THEN DATEADD(MONTH, 12, ISNULL(subscriptionStartDate, registrationDate))
                WHEN subscriptionStatus = 'Premium' THEN DATEADD(MONTH, 12, ISNULL(subscriptionStartDate, registrationDate))
                WHEN subscriptionStatus = 'Enterprise' THEN DATEADD(MONTH, 24, ISNULL(subscriptionStartDate, registrationDate))
                ELSE DATEADD(MONTH, 1, ISNULL(subscriptionStartDate, registrationDate))
            END
        ELSE subscriptionEndDate
    END
WHERE subscriptionStartDate IS NULL OR subscriptionEndDate IS NULL;

PRINT 'تم تحديث تواريخ الاشتراك';

-- عرض بنية الجدول المحدثة
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'pharmacies'
ORDER BY ORDINAL_POSITION;

PRINT 'تم الانتهاء من إصلاح أعمدة الاشتراك';
