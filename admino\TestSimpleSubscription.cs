using System;
using System.Windows.Forms;
using admino.forma;

namespace admino
{
    public partial class TestSimpleSubscriptionForm : Form
    {
        private SimpleSubscriptionControl subscriptionControl;

        public TestSimpleSubscriptionForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.subscriptionControl = new SimpleSubscriptionControl();
            this.SuspendLayout();
            
            // 
            // subscriptionControl
            // 
            this.subscriptionControl.Dock = DockStyle.Fill;
            this.subscriptionControl.Location = new System.Drawing.Point(0, 0);
            this.subscriptionControl.Name = "subscriptionControl";
            this.subscriptionControl.Size = new System.Drawing.Size(1000, 700);
            this.subscriptionControl.TabIndex = 0;
            
            // 
            // TestSimpleSubscriptionForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1000, 700);
            this.Controls.Add(this.subscriptionControl);
            this.Name = "TestSimpleSubscriptionForm";
            this.Text = "اختبار صفحة إدارة الاشتراكات - إصدار محسن";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.ResumeLayout(false);
        }
    }
}
