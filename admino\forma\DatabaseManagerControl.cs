using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class DatabaseManagerControl : UserControl
    {
        private Guna2ComboBox cmbTables;
        private Guna2DataGridView dgvData;
        private Guna2Panel pnlTop;
        private Guna2Panel pnlMain;
        private Guna2Panel pnlButtons;
        private Guna2Button btnAdd;
        private Guna2Button btnEdit;
        private Guna2Button btnDelete;
        private Guna2Button btnRefresh;
        private Guna2TextBox txtSearch;
        private Label lblTitle;
        private Label lblRecordCount;
        
        private string currentTable = "";
        private DataTable currentData;
        
        // قائمة الجداول المتاحة
        private Dictionary<string, string> availableTables = new Dictionary<string, string>
        {
            {"pharmacies", "الصيدليات"},
            {"pharmacy_users", "مستخدمو الصيدليات"},
            {"user_roles", "أدوار المستخدمين"},
            {"user_permissions", "صلاحيات المستخدمين"},
            {"user_permission_mapping", "ربط المستخدمين بالصلاحيات"},
            {"user_sessions", "جلسات المستخدمين"},
            {"user_activity_log", "سجل الأنشطة"}
        };

        public DatabaseManagerControl()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Set basic properties
            this.Name = "DatabaseManagerControl";
            this.Size = new Size(1200, 800);
            this.BackColor = Color.White;
            this.RightToLeft = RightToLeft.Yes;
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Create panels
            CreatePanels();
            
            // Create controls
            CreateControls();
            
            // Setup layout
            SetupLayout();
            
            // Load initial data
            LoadTables();
        }

        private void CreatePanels()
        {
            // Top panel for controls
            pnlTop = new Guna2Panel
            {
                Name = "pnlTop",
                Height = 80,
                Dock = DockStyle.Top,
                FillColor = Color.FromArgb(247, 248, 250),
                BorderRadius = 8,
                Margin = new Padding(10)
            };
            this.Controls.Add(pnlTop);

            // Button panel
            pnlButtons = new Guna2Panel
            {
                Name = "pnlButtons",
                Height = 60,
                Dock = DockStyle.Bottom,
                FillColor = Color.FromArgb(247, 248, 250),
                BorderRadius = 8,
                Margin = new Padding(10)
            };
            this.Controls.Add(pnlButtons);

            // Main panel for data grid
            pnlMain = new Guna2Panel
            {
                Name = "pnlMain",
                Dock = DockStyle.Fill,
                FillColor = Color.White,
                BorderRadius = 8,
                Margin = new Padding(10)
            };
            this.Controls.Add(pnlMain);
        }

        private void CreateControls()
        {
            // Title label
            lblTitle = new Label
            {
                Text = "إدارة قاعدة البيانات - UnifiedPharmacy",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64),
                Location = new Point(20, 15),
                Size = new Size(400, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };
            pnlTop.Controls.Add(lblTitle);

            // Table selection combo box
            cmbTables = new Guna2ComboBox
            {
                Name = "cmbTables",
                Location = new Point(450, 15),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 10),
                BorderRadius = 8,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbTables.SelectedIndexChanged += CmbTables_SelectedIndexChanged;
            pnlTop.Controls.Add(cmbTables);

            // Search text box
            txtSearch = new Guna2TextBox
            {
                Name = "txtSearch",
                Location = new Point(670, 15),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 10),
                BorderRadius = 8,
                PlaceholderText = "البحث..."
            };
            txtSearch.TextChanged += TxtSearch_TextChanged;
            pnlTop.Controls.Add(txtSearch);

            // Record count label
            lblRecordCount = new Label
            {
                Text = "عدد السجلات: 0",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(100, 100, 100),
                Location = new Point(900, 20),
                Size = new Size(150, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };
            pnlTop.Controls.Add(lblRecordCount);

            // Data grid view
            dgvData = new Guna2DataGridView
            {
                Name = "dgvData",
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(100, 88, 255),
                    ForeColor = Color.White,
                    SelectionBackColor = Color.FromArgb(100, 88, 255),
                    SelectionForeColor = Color.White,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(71, 69, 94),
                    SelectionBackColor = Color.FromArgb(231, 229, 255),
                    SelectionForeColor = Color.FromArgb(71, 69, 94),
                    Font = new Font("Segoe UI", 9)
                },
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(231, 229, 255),
                RowHeadersVisible = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };
            dgvData.CellDoubleClick += DgvData_CellDoubleClick;
            pnlMain.Controls.Add(dgvData);

            // Buttons
            CreateButtons();
        }

        private void CreateButtons()
        {
            int buttonWidth = 120;
            int buttonHeight = 35;
            int spacing = 15;
            int startX = 20;

            // Add button
            btnAdd = new Guna2Button
            {
                Name = "btnAdd",
                Text = "إضافة جديد",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX, 12),
                BorderRadius = 8,
                FillColor = Color.FromArgb(94, 148, 255),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White
            };
            btnAdd.Click += BtnAdd_Click;
            pnlButtons.Controls.Add(btnAdd);

            // Edit button
            btnEdit = new Guna2Button
            {
                Name = "btnEdit",
                Text = "تعديل",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX + buttonWidth + spacing, 12),
                BorderRadius = 8,
                FillColor = Color.FromArgb(255, 193, 7),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White
            };
            btnEdit.Click += BtnEdit_Click;
            pnlButtons.Controls.Add(btnEdit);

            // Delete button
            btnDelete = new Guna2Button
            {
                Name = "btnDelete",
                Text = "حذف",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX + (buttonWidth + spacing) * 2, 12),
                BorderRadius = 8,
                FillColor = Color.FromArgb(255, 69, 90),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White
            };
            btnDelete.Click += BtnDelete_Click;
            pnlButtons.Controls.Add(btnDelete);

            // Refresh button
            btnRefresh = new Guna2Button
            {
                Name = "btnRefresh",
                Text = "تحديث",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(startX + (buttonWidth + spacing) * 3, 12),
                BorderRadius = 8,
                FillColor = Color.FromArgb(40, 167, 69),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White
            };
            btnRefresh.Click += BtnRefresh_Click;
            pnlButtons.Controls.Add(btnRefresh);
        }

        private void SetupLayout()
        {
            // Ensure proper docking order
            pnlTop.BringToFront();
            pnlButtons.BringToFront();
            pnlMain.BringToFront();
        }

        private void LoadTables()
        {
            try
            {
                cmbTables.Items.Clear();

                foreach (var table in availableTables)
                {
                    cmbTables.Items.Add($"{table.Value} ({table.Key})");
                }

                if (cmbTables.Items.Count > 0)
                {
                    cmbTables.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الجداول: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CmbTables_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbTables.SelectedIndex >= 0)
            {
                string selectedText = cmbTables.SelectedItem.ToString();
                // Extract table name from "Arabic Name (table_name)" format
                int startIndex = selectedText.LastIndexOf('(') + 1;
                int endIndex = selectedText.LastIndexOf(')');

                if (startIndex > 0 && endIndex > startIndex)
                {
                    currentTable = selectedText.Substring(startIndex, endIndex - startIndex);
                    LoadTableData();
                }
            }
        }

        private void LoadTableData()
        {
            if (string.IsNullOrEmpty(currentTable))
                return;

            try
            {
                this.Cursor = Cursors.WaitCursor;

                string query = $"SELECT * FROM {currentTable}";
                currentData = DatabaseConnection.ExecutePharmacyQuery(query);

                dgvData.DataSource = currentData;

                // Configure columns based on table
                ConfigureColumnsForTable();

                // Update record count
                lblRecordCount.Text = $"عدد السجلات: {currentData.Rows.Count}";

                // Update title
                string tableNameAr = availableTables.ContainsKey(currentTable) ?
                                   availableTables[currentTable] : currentTable;
                lblTitle.Text = $"إدارة قاعدة البيانات - {tableNameAr}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الجدول {currentTable}: {ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                dgvData.DataSource = null;
                lblRecordCount.Text = "عدد السجلات: 0";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void ConfigureColumnsForTable()
        {
            if (dgvData.Columns.Count == 0) return;

            // Common column configurations
            foreach (DataGridViewColumn column in dgvData.Columns)
            {
                // Set Arabic headers based on common column names
                switch (column.Name.ToLower())
                {
                    case "id":
                        column.HeaderText = "المعرف";
                        column.Width = 60;
                        break;
                    case "pharmacyid":
                        column.HeaderText = "معرف الصيدلية";
                        column.Width = 100;
                        break;
                    case "pharmacycode":
                        column.HeaderText = "كود الصيدلية";
                        column.Width = 120;
                        break;
                    case "pharmacyname":
                        column.HeaderText = "اسم الصيدلية (EN)";
                        column.Width = 150;
                        break;
                    case "pharmacynamear":
                        column.HeaderText = "اسم الصيدلية";
                        column.Width = 150;
                        break;
                    case "ownername":
                        column.HeaderText = "اسم المالك (EN)";
                        column.Width = 120;
                        break;
                    case "ownernamear":
                        column.HeaderText = "اسم المالك";
                        column.Width = 120;
                        break;
                    case "username":
                        column.HeaderText = "اسم المستخدم";
                        column.Width = 100;
                        break;
                    case "fullname":
                        column.HeaderText = "الاسم الكامل (EN)";
                        column.Width = 120;
                        break;
                    case "fullnamear":
                        column.HeaderText = "الاسم الكامل";
                        column.Width = 120;
                        break;
                    case "email":
                        column.HeaderText = "البريد الإلكتروني";
                        column.Width = 150;
                        break;
                    case "phone":
                        column.HeaderText = "الهاتف";
                        column.Width = 100;
                        break;
                    case "mobile":
                        column.HeaderText = "الجوال";
                        column.Width = 100;
                        break;
                    case "address":
                        column.HeaderText = "العنوان (EN)";
                        column.Width = 150;
                        break;
                    case "addressar":
                        column.HeaderText = "العنوان";
                        column.Width = 150;
                        break;
                    case "city":
                        column.HeaderText = "المدينة (EN)";
                        column.Width = 100;
                        break;
                    case "cityar":
                        column.HeaderText = "المدينة";
                        column.Width = 100;
                        break;
                    case "status":
                        column.HeaderText = "الحالة";
                        column.Width = 80;
                        break;
                    case "isactive":
                        column.HeaderText = "نشط";
                        column.Width = 60;
                        break;
                    case "createddate":
                        column.HeaderText = "تاريخ الإنشاء";
                        column.Width = 120;
                        break;
                    case "rolename":
                        column.HeaderText = "اسم الدور (EN)";
                        column.Width = 100;
                        break;
                    case "rolenamear":
                        column.HeaderText = "اسم الدور";
                        column.Width = 100;
                        break;
                    case "permissionname":
                        column.HeaderText = "اسم الصلاحية (EN)";
                        column.Width = 120;
                        break;
                    case "permissionnamear":
                        column.HeaderText = "اسم الصلاحية";
                        column.Width = 120;
                        break;
                    case "description":
                        column.HeaderText = "الوصف (EN)";
                        column.Width = 150;
                        break;
                    case "descriptionar":
                        column.HeaderText = "الوصف";
                        column.Width = 150;
                        break;
                    case "lastlogin":
                        column.HeaderText = "آخر تسجيل دخول";
                        column.Width = 130;
                        break;
                    case "sessiontoken":
                        column.HeaderText = "رمز الجلسة";
                        column.Width = 100;
                        break;
                    case "action":
                        column.HeaderText = "الإجراء (EN)";
                        column.Width = 100;
                        break;
                    case "actionar":
                        column.HeaderText = "الإجراء";
                        column.Width = 100;
                        break;
                    default:
                        // Keep original name if no translation found
                        break;
                }
            }

            // Hide sensitive columns
            HideSensitiveColumns();
        }

        private void HideSensitiveColumns()
        {
            // Hide password and sensitive data columns
            string[] sensitiveColumns = { "password", "sessiontoken", "oldvalues", "newvalues" };

            foreach (string columnName in sensitiveColumns)
            {
                if (dgvData.Columns.Contains(columnName))
                {
                    dgvData.Columns[columnName].Visible = false;
                }
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            if (currentData == null) return;

            try
            {
                string searchText = txtSearch.Text.Trim();

                if (string.IsNullOrEmpty(searchText))
                {
                    dgvData.DataSource = currentData;
                }
                else
                {
                    // Create filter for all string columns
                    List<string> filters = new List<string>();

                    foreach (DataColumn column in currentData.Columns)
                    {
                        if (column.DataType == typeof(string))
                        {
                            filters.Add($"[{column.ColumnName}] LIKE '%{searchText}%'");
                        }
                    }

                    if (filters.Count > 0)
                    {
                        string filter = string.Join(" OR ", filters);
                        DataView dv = new DataView(currentData);
                        dv.RowFilter = filter;
                        dgvData.DataSource = dv;

                        lblRecordCount.Text = $"عدد السجلات: {dv.Count} من {currentData.Rows.Count}";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void DgvData_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditSelectedRecord();
            }
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            AddNewRecord();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            EditSelectedRecord();
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DeleteSelectedRecord();
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadTableData();
        }

        private void AddNewRecord()
        {
            if (string.IsNullOrEmpty(currentTable))
            {
                MessageBox.Show("يرجى اختيار جدول أولاً", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // Open generic record editor form
                using (var form = new DatabaseRecordEditorForm(currentTable, null, false))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        LoadTableData(); // Refresh data
                        MessageBox.Show("تم إضافة السجل بنجاح", "نجح",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة السجل: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void EditSelectedRecord()
        {
            if (dgvData.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار سجل للتعديل", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                DataGridViewRow selectedRow = dgvData.SelectedRows[0];
                DataRowView rowView = selectedRow.DataBoundItem as DataRowView;

                if (rowView != null)
                {
                    // Open generic record editor form
                    using (var form = new DatabaseRecordEditorForm(currentTable, rowView.Row, true))
                    {
                        if (form.ShowDialog() == DialogResult.OK)
                        {
                            LoadTableData(); // Refresh data
                            MessageBox.Show("تم تحديث السجل بنجاح", "نجح",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل السجل: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedRecord()
        {
            if (dgvData.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار سجل للحذف", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                DataGridViewRow selectedRow = dgvData.SelectedRows[0];
                DataRowView rowView = selectedRow.DataBoundItem as DataRowView;

                if (rowView != null)
                {
                    // Get primary key value
                    object idValue = rowView.Row["id"];

                    if (idValue == null || idValue == DBNull.Value)
                    {
                        MessageBox.Show("لا يمكن تحديد معرف السجل", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // Confirm deletion
                    string tableName = availableTables.ContainsKey(currentTable) ?
                                     availableTables[currentTable] : currentTable;

                    DialogResult result = MessageBox.Show(
                        $"هل أنت متأكد من حذف هذا السجل من جدول {tableName}؟\n\n" +
                        "تحذير: هذا الإجراء لا يمكن التراجع عنه!",
                        "تأكيد الحذف",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Execute delete query
                        string deleteQuery = $"DELETE FROM {currentTable} WHERE id = @id";
                        SqlParameter[] parameters = {
                            new SqlParameter("@id", idValue)
                        };

                        int rowsAffected = DatabaseConnection.ExecutePharmacyNonQuery(deleteQuery, parameters);

                        if (rowsAffected > 0)
                        {
                            LoadTableData(); // Refresh data
                            MessageBox.Show("تم حذف السجل بنجاح", "نجح",
                                          MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("لم يتم حذف أي سجل", "تنبيه",
                                          MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السجل: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
