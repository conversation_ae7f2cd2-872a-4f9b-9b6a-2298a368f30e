-- =============================================
-- إصلاح سريع لمشكلة الأعمدة المفقودة
-- Quick Fix for Missing Columns Issue
-- =============================================

USE UnifiedPharmacy;
GO

PRINT '🔧 بدء الإصلاح السريع...';
PRINT '========================';

-- التحقق من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
PRINT '1️⃣ التحقق من الأعمدة المطلوبة...';

-- إضافة عمود subscriptionStartDate
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionStartDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionStartDate DATETIME2 DEFAULT GETDATE();
    PRINT '✅ تم إضافة عمود subscriptionStartDate';
END
ELSE
BEGIN
    PRINT '✅ عمود subscriptionStartDate موجود بالفعل';
END

-- إضافة عمود subscriptionEndDate
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'subscriptionEndDate')
BEGIN
    ALTER TABLE pharmacies ADD subscriptionEndDate DATETIME2;
    PRINT '✅ تم إضافة عمود subscriptionEndDate';
END
ELSE
BEGIN
    PRINT '✅ عمود subscriptionEndDate موجود بالفعل';
END

-- إضافة عمود pharmacyCode
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'pharmacies' AND COLUMN_NAME = 'pharmacyCode')
BEGIN
    ALTER TABLE pharmacies ADD pharmacyCode NVARCHAR(20);
    PRINT '✅ تم إضافة عمود pharmacyCode';
END
ELSE
BEGIN
    PRINT '✅ عمود pharmacyCode موجود بالفعل';
END

-- تحديث البيانات الموجودة
PRINT '2️⃣ تحديث البيانات الموجودة...';

-- تحديث أكواد الصيدليات للصيدليات التي لا تحتوي على كود
UPDATE pharmacies 
SET pharmacyCode = 'PHM' + RIGHT('000' + CAST(id AS VARCHAR(3)), 3)
WHERE pharmacyCode IS NULL OR pharmacyCode = '';

-- تحديث تواريخ الاشتراك للصيدليات التي لا تحتوي على تواريخ
UPDATE pharmacies 
SET subscriptionStartDate = ISNULL(subscriptionStartDate, registrationDate)
WHERE subscriptionStartDate IS NULL;

UPDATE pharmacies 
SET subscriptionEndDate = CASE 
    WHEN subscriptionEndDate IS NULL THEN
        CASE 
            WHEN ISNULL(subscriptionStatus, 'Trial') = 'Trial' THEN DATEADD(DAY, 30, ISNULL(subscriptionStartDate, registrationDate))
            WHEN ISNULL(subscriptionStatus, 'Trial') = 'Basic' THEN DATEADD(MONTH, 12, ISNULL(subscriptionStartDate, registrationDate))
            WHEN ISNULL(subscriptionStatus, 'Trial') = 'Premium' THEN DATEADD(MONTH, 12, ISNULL(subscriptionStartDate, registrationDate))
            WHEN ISNULL(subscriptionStatus, 'Trial') = 'Enterprise' THEN DATEADD(MONTH, 24, ISNULL(subscriptionStartDate, registrationDate))
            ELSE DATEADD(MONTH, 1, ISNULL(subscriptionStartDate, registrationDate))
        END
    ELSE subscriptionEndDate
END
WHERE subscriptionEndDate IS NULL;

PRINT '✅ تم تحديث البيانات الموجودة';

-- عرض النتائج
PRINT '3️⃣ عرض النتائج...';

DECLARE @PharmacyCount INT;
SELECT @PharmacyCount = COUNT(*) FROM pharmacies;
PRINT '📊 عدد الصيدليات في النظام: ' + CAST(@PharmacyCount AS VARCHAR(10));

IF @PharmacyCount > 0
BEGIN
    PRINT '📋 قائمة الصيدليات الموجودة:';
    SELECT TOP 5
        id as 'الرقم',
        pharmacyCode as 'كود الصيدلية',
        ISNULL(pharmacyNameAr, pharmacyName) as 'اسم الصيدلية',
        ISNULL(subscriptionStatus, 'Trial') as 'نوع الاشتراك',
        FORMAT(ISNULL(subscriptionStartDate, registrationDate), 'yyyy/MM/dd') as 'تاريخ البداية',
        FORMAT(subscriptionEndDate, 'yyyy/MM/dd') as 'تاريخ الانتهاء',
        CASE 
            WHEN subscriptionEndDate > GETDATE() THEN 'نشط'
            ELSE 'منتهي'
        END as 'حالة الاشتراك',
        ISNULL(status, 'Pending') as 'حالة الصيدلية'
    FROM pharmacies
    ORDER BY registrationDate DESC;
END
ELSE
BEGIN
    PRINT '⚠️ لا توجد صيدليات مسجلة في النظام';
END

PRINT '========================';
PRINT '🎉 تم الانتهاء من الإصلاح السريع بنجاح!';
PRINT '========================';
