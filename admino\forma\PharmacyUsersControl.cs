using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace admino.forma
{
    public partial class PharmacyUsersControl : UserControl
    {
        private Guna2Panel mainPanel;
        private Guna2Panel headerPanel;
        private Guna2Panel searchPanel;
        private Guna2Panel contentPanel;
        private Guna2Panel buttonPanel;
        
        private Label lblTitle;
        private Guna2TextBox txtSearch;
        private Guna2ComboBox cmbPharmacyFilter;
        private Guna2ComboBox cmbRoleFilter;
        private Guna2ComboBox cmbStatusFilter;
        private Guna2Button btnSearch;
        private Guna2Button btnClear;
        private Guna2Button btnAddUser;
        private Guna2Button btnEditUser;
        private Guna2Button btnDeleteUser;
        private Guna2Button btnRefresh;
        
        private Guna2DataGridView dgvUsers;
        
        private DataTable usersData;

        public PharmacyUsersControl()
        {
            try
            {
                InitializeComponent();
                InitializeCustomComponents();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الحسابات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Initialize with empty data to prevent black screen
                InitializeComponent();
                InitializeCustomComponents();
                usersData = CreateEmptyDataTable();
                SetupDataGridViewColumns();
                dgvUsers.DataSource = usersData;
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Main settings
            this.Size = new Size(1200, 800);
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.Dock = DockStyle.Fill;
            
            this.ResumeLayout(false);
        }

        private void InitializeCustomComponents()
        {
            // Main Panel
            mainPanel = new Guna2Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(240, 244, 247),
                Padding = new Padding(20)
            };
            this.Controls.Add(mainPanel);

            // Header Panel
            headerPanel = new Guna2Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                BorderRadius = 10
            };
            headerPanel.ShadowDecoration.Enabled = true;
            mainPanel.Controls.Add(headerPanel);

            // Title
            lblTitle = new Label
            {
                Text = "إدارة حسابات الصيدليات",
                Font = new Font("Segoe UI", 18F, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Location = new Point(30, 25),
                AutoSize = true
            };
            headerPanel.Controls.Add(lblTitle);

            // Search Panel
            searchPanel = new Guna2Panel
            {
                Height = 100,
                Dock = DockStyle.Top,
                BackColor = Color.White,
                BorderRadius = 10
            };
            searchPanel.ShadowDecoration.Enabled = true;
            searchPanel.Margin = new Padding(0, 10, 0, 0);
            mainPanel.Controls.Add(searchPanel);

            InitializeSearchControls();

            // Content Panel
            contentPanel = new Guna2Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                BorderRadius = 10
            };
            contentPanel.ShadowDecoration.Enabled = true;
            contentPanel.Margin = new Padding(0, 10, 0, 0);
            mainPanel.Controls.Add(contentPanel);

            // Button Panel
            buttonPanel = new Guna2Panel
            {
                Height = 70,
                Dock = DockStyle.Bottom,
                BackColor = Color.White,
                BorderRadius = 10
            };
            buttonPanel.ShadowDecoration.Enabled = true;
            buttonPanel.Margin = new Padding(0, 10, 0, 0);
            mainPanel.Controls.Add(buttonPanel);

            InitializeButtons();
            InitializeDataGridView();
        }

        private void InitializeSearchControls()
        {
            // Search TextBox
            txtSearch = new Guna2TextBox
            {
                Location = new Point(30, 30),
                Size = new Size(250, 40),
                PlaceholderText = "البحث بالاسم أو اسم المستخدم...",
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F)
            };
            searchPanel.Controls.Add(txtSearch);

            // Pharmacy Filter
            cmbPharmacyFilter = new Guna2ComboBox
            {
                Location = new Point(300, 30),
                Size = new Size(200, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F)
            };
            searchPanel.Controls.Add(cmbPharmacyFilter);

            // Role Filter
            cmbRoleFilter = new Guna2ComboBox
            {
                Location = new Point(520, 30),
                Size = new Size(150, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F)
            };
            searchPanel.Controls.Add(cmbRoleFilter);

            // Status Filter
            cmbStatusFilter = new Guna2ComboBox
            {
                Location = new Point(690, 30),
                Size = new Size(120, 40),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10F)
            };
            cmbStatusFilter.Items.AddRange(new string[] { "الكل", "نشط", "غير نشط" });
            cmbStatusFilter.SelectedIndex = 0;
            searchPanel.Controls.Add(cmbStatusFilter);

            // Search Button
            btnSearch = new Guna2Button
            {
                Location = new Point(830, 30),
                Size = new Size(100, 40),
                Text = "بحث",
                BorderRadius = 8,
                FillColor = Color.FromArgb(52, 152, 219),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnSearch.Click += BtnSearch_Click;
            searchPanel.Controls.Add(btnSearch);

            // Clear Button
            btnClear = new Guna2Button
            {
                Location = new Point(950, 30),
                Size = new Size(100, 40),
                Text = "مسح",
                BorderRadius = 8,
                FillColor = Color.FromArgb(149, 165, 166),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnClear.Click += BtnClear_Click;
            searchPanel.Controls.Add(btnClear);
        }

        private void InitializeButtons()
        {
            int buttonY = 15;
            int buttonHeight = 40;
            int buttonWidth = 120;
            int spacing = 10;

            // Add User Button
            btnAddUser = new Guna2Button
            {
                Location = new Point(30, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = "إضافة مستخدم",
                BorderRadius = 8,
                FillColor = Color.FromArgb(46, 204, 113),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnAddUser.Click += BtnAddUser_Click;
            buttonPanel.Controls.Add(btnAddUser);

            // Edit User Button
            btnEditUser = new Guna2Button
            {
                Location = new Point(30 + buttonWidth + spacing, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = "تعديل مستخدم",
                BorderRadius = 8,
                FillColor = Color.FromArgb(241, 196, 15),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnEditUser.Click += BtnEditUser_Click;
            buttonPanel.Controls.Add(btnEditUser);

            // Delete User Button
            btnDeleteUser = new Guna2Button
            {
                Location = new Point(30 + (buttonWidth + spacing) * 2, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = "حذف مستخدم",
                BorderRadius = 8,
                FillColor = Color.FromArgb(231, 76, 60),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnDeleteUser.Click += BtnDeleteUser_Click;
            buttonPanel.Controls.Add(btnDeleteUser);

            // Refresh Button
            btnRefresh = new Guna2Button
            {
                Location = new Point(30 + (buttonWidth + spacing) * 3, buttonY),
                Size = new Size(buttonWidth, buttonHeight),
                Text = "تحديث",
                BorderRadius = 8,
                FillColor = Color.FromArgb(52, 152, 219),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnRefresh.Click += BtnRefresh_Click;
            buttonPanel.Controls.Add(btnRefresh);
        }

        private void InitializeDataGridView()
        {
            dgvUsers = new Guna2DataGridView
            {
                Dock = DockStyle.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
                ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 152, 219),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 11F, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                ColumnHeadersHeight = 50,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(44, 62, 80),
                    Font = new Font("Segoe UI", 10F),
                    SelectionBackColor = Color.FromArgb(231, 242, 254),
                    SelectionForeColor = Color.FromArgb(44, 62, 80),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                EnableHeadersVisualStyles = false,
                GridColor = Color.FromArgb(231, 242, 254),
                RowHeadersVisible = false,
                RowTemplate = { Height = 45 },
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                MultiSelect = false
            };

            dgvUsers.Margin = new Padding(20);
            contentPanel.Controls.Add(dgvUsers);
        }

        private void LoadData()
        {
            try
            {
                // Test database connection first
                if (!DatabaseConnection.TestPharmacyConnection())
                {
                    MessageBox.Show("خطأ في الاتصال بقاعدة البيانات", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);

                    // Create empty table to prevent black screen
                    usersData = CreateEmptyDataTable();
                    SetupDataGridViewColumns();
                    dgvUsers.DataSource = usersData;
                    return;
                }

                // Load users data
                usersData = PharmacyUserManager.GetAllPharmacyUsers();

                if (usersData == null || usersData.Rows.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات مستخدمين في قاعدة البيانات", "تنبيه",
                                  MessageBoxButtons.OK, MessageBoxIcon.Information);
                    usersData = CreateEmptyDataTable(); // Create empty table to avoid null reference
                }

                SetupDataGridViewColumns();
                dgvUsers.DataSource = usersData;

                // Load filter data
                LoadPharmacyFilter();
                LoadRoleFilter();

                // Update UI
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Create empty table to prevent black screen
                usersData = CreateEmptyDataTable();
                SetupDataGridViewColumns();
                dgvUsers.DataSource = usersData;
            }
        }

        private DataTable CreateEmptyDataTable()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("id", typeof(int));
            dt.Columns.Add("pharmacyCode", typeof(string));
            dt.Columns.Add("pharmacyNameAr", typeof(string));
            dt.Columns.Add("fullNameAr", typeof(string));
            dt.Columns.Add("username", typeof(string));
            dt.Columns.Add("roleNameAr", typeof(string));
            dt.Columns.Add("email", typeof(string));
            dt.Columns.Add("mobile", typeof(string));
            dt.Columns.Add("isActive", typeof(bool));
            dt.Columns.Add("lastLogin", typeof(DateTime));
            return dt;
        }

        private void SetupDataGridViewColumns()
        {
            dgvUsers.AutoGenerateColumns = false;
            dgvUsers.Columns.Clear();

            // ID Column (Hidden)
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "id",
                Name = "Id",
                Visible = false
            });

            // Pharmacy Code
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "pharmacyCode",
                Name = "PharmacyCode",
                HeaderText = "كود الصيدلية",
                Width = 100,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // Pharmacy Name
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "pharmacyNameAr",
                Name = "PharmacyName",
                HeaderText = "اسم الصيدلية",
                Width = 180,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // Full Name
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "fullNameAr",
                Name = "FullName",
                HeaderText = "الاسم",
                Width = 150,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleRight }
            });

            // Username
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "username",
                Name = "Username",
                HeaderText = "اسم المستخدم",
                Width = 120,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // Role
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "roleNameAr",
                Name = "Role",
                HeaderText = "الدور",
                Width = 100,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // Email
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "email",
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                Width = 180,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleLeft }
            });

            // Mobile
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "mobile",
                Name = "Mobile",
                HeaderText = "الجوال",
                Width = 110,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // Status
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Status",
                HeaderText = "الحالة",
                Width = 100,
                DefaultCellStyle = { Alignment = DataGridViewContentAlignment.MiddleCenter }
            });

            // Last Login
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                DataPropertyName = "lastLogin",
                Name = "LastLogin",
                HeaderText = "آخر دخول",
                Width = 120,
                DefaultCellStyle = {
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Format = "dd/MM/yyyy"
                }
            });

            // Handle cell formatting and selection
            dgvUsers.CellFormatting += DgvUsers_CellFormatting;
            dgvUsers.SelectionChanged += dgvUsers_SelectionChanged;
        }

        private void DgvUsers_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

                if (dgvUsers.Columns[e.ColumnIndex].Name == "Status")
                {
                    var row = dgvUsers.Rows[e.RowIndex];
                    var isActiveCell = row.Cells["isActive"];

                    if (isActiveCell?.Value != null && isActiveCell.Value != DBNull.Value)
                    {
                        bool isActive = Convert.ToBoolean(isActiveCell.Value);

                        if (!isActive)
                        {
                            e.Value = "غير نشط";
                            e.CellStyle.ForeColor = Color.FromArgb(231, 76, 60);
                        }
                        else
                        {
                            e.Value = "نشط";
                            e.CellStyle.ForeColor = Color.FromArgb(46, 204, 113);
                        }
                    }
                    else
                    {
                        e.Value = "غير محدد";
                        e.CellStyle.ForeColor = Color.Gray;
                    }
                }
            }
            catch (Exception ex)
            {
                // Ignore formatting errors to prevent crashes
                e.Value = "خطأ";
            }
        }

        private void LoadPharmacyFilter()
        {
            try
            {
                cmbPharmacyFilter.Items.Clear();
                cmbPharmacyFilter.Items.Add("جميع الصيدليات");

                var pharmacies = PharmacyManager.GetAllPharmacies();
                if (pharmacies != null && pharmacies.Rows.Count > 0)
                {
                    foreach (DataRow row in pharmacies.Rows)
                    {
                        string pharmacyName = row["pharmacyNameAr"]?.ToString() ?? row["pharmacyName"]?.ToString() ?? "صيدلية غير محددة";
                        string pharmacyCode = row["pharmacyCode"]?.ToString() ?? "";
                        cmbPharmacyFilter.Items.Add($"{pharmacyName} ({pharmacyCode})");
                    }
                }

                cmbPharmacyFilter.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                cmbPharmacyFilter.Items.Clear();
                cmbPharmacyFilter.Items.Add("جميع الصيدليات");
                cmbPharmacyFilter.SelectedIndex = 0;
            }
        }

        private void LoadRoleFilter()
        {
            try
            {
                cmbRoleFilter.Items.Clear();
                cmbRoleFilter.Items.Add("جميع الأدوار");

                var roles = PharmacyUserManager.GetAllUserRoles();
                if (roles != null && roles.Rows.Count > 0)
                {
                    foreach (DataRow row in roles.Rows)
                    {
                        string roleName = row["roleNameAr"]?.ToString() ?? row["roleName"]?.ToString() ?? "دور غير محدد";
                        if (!string.IsNullOrEmpty(roleName) && roleName != "دور غير محدد")
                        {
                            cmbRoleFilter.Items.Add(roleName);
                        }
                    }
                }

                cmbRoleFilter.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                cmbRoleFilter.Items.Clear();
                cmbRoleFilter.Items.Add("جميع الأدوار");
                cmbRoleFilter.SelectedIndex = 0;
            }
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = dgvUsers.SelectedRows.Count > 0;
            btnEditUser.Enabled = hasSelection;
            btnDeleteUser.Enabled = hasSelection;
        }

        // Event Handlers
        private void BtnSearch_Click(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            txtSearch.Clear();
            cmbPharmacyFilter.SelectedIndex = 0;
            cmbRoleFilter.SelectedIndex = 0;
            cmbStatusFilter.SelectedIndex = 0;
            ApplyFilters();
        }

        private void BtnAddUser_Click(object sender, EventArgs e)
        {
            var addForm = new AddEditPharmacyUserForm();
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
                MessageBox.Show("تم إضافة المستخدم بنجاح", "نجح",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnEditUser_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للتعديل", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["Id"].Value);
            var editForm = new AddEditPharmacyUserForm(userId);
            if (editForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
                MessageBox.Show("تم تحديث المستخدم بنجاح", "نجح",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnDeleteUser_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للحذف", "تنبيه",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا المستخدم؟\nهذا الإجراء لا يمكن التراجع عنه.",
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    int userId = Convert.ToInt32(dgvUsers.SelectedRows[0].Cells["Id"].Value);
                    bool success = PharmacyUserManager.DeletePharmacyUser(userId);

                    if (success)
                    {
                        LoadData();
                        MessageBox.Show("تم حذف المستخدم بنجاح", "نجح",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المستخدم", "خطأ",
                                      MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void ApplyFilters()
        {
            if (usersData == null || usersData.Rows.Count == 0) return;

            try
            {
                string filter = "1=1";

                // Search filter
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    string searchText = txtSearch.Text.Trim().Replace("'", "''"); // Escape single quotes
                    filter += $" AND (fullNameAr LIKE '%{searchText}%' OR username LIKE '%{searchText}%')";
                }

                // Pharmacy filter
                if (cmbPharmacyFilter.SelectedIndex > 0 && cmbPharmacyFilter.SelectedItem != null)
                {
                    string selectedPharmacy = cmbPharmacyFilter.SelectedItem.ToString();
                    // Extract pharmacy code from the selection
                    if (selectedPharmacy.Contains("(") && selectedPharmacy.Contains(")"))
                    {
                        string pharmacyCode = selectedPharmacy.Substring(selectedPharmacy.LastIndexOf("(") + 1);
                        pharmacyCode = pharmacyCode.Replace(")", "").Replace("'", "''");
                        filter += $" AND pharmacyCode = '{pharmacyCode}'";
                    }
                }

                // Role filter
                if (cmbRoleFilter.SelectedIndex > 0 && cmbRoleFilter.SelectedItem != null)
                {
                    string selectedRole = cmbRoleFilter.SelectedItem.ToString().Replace("'", "''");
                    filter += $" AND roleNameAr = '{selectedRole}'";
                }

                // Status filter
                if (cmbStatusFilter.SelectedIndex > 0 && cmbStatusFilter.SelectedItem != null)
                {
                    string selectedStatus = cmbStatusFilter.SelectedItem.ToString();
                    switch (selectedStatus)
                    {
                        case "نشط":
                            filter += " AND isActive = True";
                            break;
                        case "غير نشط":
                            filter += " AND isActive = False";
                            break;
                    }
                }

                usersData.DefaultView.RowFilter = filter;
            }
            catch (Exception ex)
            {
                // Reset filter on error
                try
                {
                    usersData.DefaultView.RowFilter = "";
                }
                catch { }
            }
        }

        private void dgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }
    }
}
