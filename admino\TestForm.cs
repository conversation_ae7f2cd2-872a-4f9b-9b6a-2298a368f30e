using System;
using System.Drawing;
using System.Windows.Forms;
using Guna.UI2.WinForms;
using admino.forma;

namespace admino
{
    public partial class TestForm : Form
    {
        private add_pharmacy addPharmacyControl;
        private uc_pharmacylist pharmacyListControl;
        private PharmacyUsersControl pharmacyUsersControl;
        private DatabaseManagerControl databaseManagerControl;
        private forma.SubscriptionManagementControl subscriptionManagementControl;

        public TestForm()
        {
            InitializeComponent();
            this.WindowState = FormWindowState.Maximized;
            this.Text = $"نظام إدارة الصيدليات - مرحباً {AdminUserManager.CurrentUser?.FullNameAr}";
            
            ShowPharmacyList();
        }

        private void InitializeComponent()
        {
            this.guna2Panel1 = new Guna.UI2.WinForms.Guna2Panel();
            this.lblWelcome = new System.Windows.Forms.Label();
            this.btnLogout = new Guna.UI2.WinForms.Guna2Button();
            this.btnTestDatabase = new Guna.UI2.WinForms.Guna2Button();
            this.btnPharmacyList = new Guna.UI2.WinForms.Guna2Button();
            this.btnAddPharmacy = new Guna.UI2.WinForms.Guna2Button();
            this.btnPharmacyUsers = new Guna.UI2.WinForms.Guna2Button();
            this.btnSubscriptions = new Guna.UI2.WinForms.Guna2Button();
            this.btnDatabaseManager = new Guna.UI2.WinForms.Guna2Button();
            this.panelMain = new System.Windows.Forms.Panel();
            this.guna2Panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // guna2Panel1
            // 
            this.guna2Panel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(118)))), ((int)(((byte)(212)))));
            this.guna2Panel1.Controls.Add(this.lblWelcome);
            this.guna2Panel1.Controls.Add(this.btnLogout);
            this.guna2Panel1.Controls.Add(this.btnTestDatabase);
            this.guna2Panel1.Controls.Add(this.btnDatabaseManager);
            this.guna2Panel1.Controls.Add(this.btnSubscriptions);
            this.guna2Panel1.Controls.Add(this.btnPharmacyUsers);
            this.guna2Panel1.Controls.Add(this.btnPharmacyList);
            this.guna2Panel1.Controls.Add(this.btnAddPharmacy);
            this.guna2Panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.guna2Panel1.Location = new System.Drawing.Point(0, 0);
            this.guna2Panel1.Name = "guna2Panel1";
            this.guna2Panel1.Size = new System.Drawing.Size(1200, 80);
            this.guna2Panel1.TabIndex = 0;
            // 
            // lblWelcome
            // 
            this.lblWelcome.AutoSize = true;
            this.lblWelcome.Font = new System.Drawing.Font("Segoe UI", 14F, System.Drawing.FontStyle.Bold);
            this.lblWelcome.ForeColor = System.Drawing.Color.White;
            this.lblWelcome.Location = new System.Drawing.Point(20, 25);
            this.lblWelcome.Name = "lblWelcome";
            this.lblWelcome.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblWelcome.Size = new System.Drawing.Size(239, 25);
            this.lblWelcome.TabIndex = 0;
            this.lblWelcome.Text = "نظام إدارة الصيدليات المركزي";
            // 
            // btnLogout
            // 
            this.btnLogout.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnLogout.BorderRadius = 8;
            this.btnLogout.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnLogout.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnLogout.ForeColor = System.Drawing.Color.White;
            this.btnLogout.Location = new System.Drawing.Point(1030, 20);
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.Size = new System.Drawing.Size(150, 40);
            this.btnLogout.TabIndex = 4;
            this.btnLogout.Text = "تسجيل خروج";
            this.btnLogout.Click += new System.EventHandler(this.btnLogout_Click);
            // 
            // btnTestDatabase
            // 
            this.btnTestDatabase.BorderRadius = 8;
            this.btnTestDatabase.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.btnTestDatabase.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnTestDatabase.ForeColor = System.Drawing.Color.White;
            this.btnTestDatabase.Location = new System.Drawing.Point(740, 20);
            this.btnTestDatabase.Name = "btnTestDatabase";
            this.btnTestDatabase.Size = new System.Drawing.Size(150, 40);
            this.btnTestDatabase.TabIndex = 3;
            this.btnTestDatabase.Text = "اختبار قاعدة البيانات";
            this.btnTestDatabase.Click += new System.EventHandler(this.btnTestDatabase_Click);
            // 
            // btnPharmacyList
            // 
            this.btnPharmacyList.BorderRadius = 8;
            this.btnPharmacyList.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnPharmacyList.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnPharmacyList.ForeColor = System.Drawing.Color.White;
            this.btnPharmacyList.Location = new System.Drawing.Point(570, 20);
            this.btnPharmacyList.Name = "btnPharmacyList";
            this.btnPharmacyList.Size = new System.Drawing.Size(150, 40);
            this.btnPharmacyList.TabIndex = 2;
            this.btnPharmacyList.Text = "قائمة الصيدليات";
            this.btnPharmacyList.Click += new System.EventHandler(this.btnPharmacyList_Click);
            // 
            // btnAddPharmacy
            // 
            this.btnAddPharmacy.BorderRadius = 8;
            this.btnAddPharmacy.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnAddPharmacy.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnAddPharmacy.ForeColor = System.Drawing.Color.White;
            this.btnAddPharmacy.Location = new System.Drawing.Point(400, 20);
            this.btnAddPharmacy.Name = "btnAddPharmacy";
            this.btnAddPharmacy.Size = new System.Drawing.Size(150, 40);
            this.btnAddPharmacy.TabIndex = 1;
            this.btnAddPharmacy.Text = "إضافة صيدلية";
            this.btnAddPharmacy.Click += new System.EventHandler(this.btnAddPharmacy_Click);
            //
            // btnPharmacyUsers
            //
            this.btnPharmacyUsers.BorderRadius = 8;
            this.btnPharmacyUsers.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(126)))), ((int)(((byte)(34)))));
            this.btnPharmacyUsers.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnPharmacyUsers.ForeColor = System.Drawing.Color.White;
            this.btnPharmacyUsers.Location = new System.Drawing.Point(230, 20);
            this.btnPharmacyUsers.Name = "btnPharmacyUsers";
            this.btnPharmacyUsers.Size = new System.Drawing.Size(150, 40);
            this.btnPharmacyUsers.TabIndex = 4;
            this.btnPharmacyUsers.Text = "حسابات الصيدليات";
            this.btnPharmacyUsers.Click += new System.EventHandler(this.btnPharmacyUsers_Click);
            //
            // btnSubscriptions
            //
            this.btnSubscriptions.BorderRadius = 8;
            this.btnSubscriptions.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(241)))), ((int)(((byte)(196)))), ((int)(((byte)(15)))));
            this.btnSubscriptions.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnSubscriptions.ForeColor = System.Drawing.Color.White;
            this.btnSubscriptions.Location = new System.Drawing.Point(530, 20);
            this.btnSubscriptions.Name = "btnSubscriptions";
            this.btnSubscriptions.Size = new System.Drawing.Size(150, 40);
            this.btnSubscriptions.TabIndex = 5;
            this.btnSubscriptions.Text = "إدارة الاشتراكات";
            this.btnSubscriptions.Click += new System.EventHandler(this.btnSubscriptions_Click);
            //
            // btnDatabaseManager
            //
            this.btnDatabaseManager.BorderRadius = 8;
            this.btnDatabaseManager.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(89)))), ((int)(((byte)(182)))));
            this.btnDatabaseManager.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
            this.btnDatabaseManager.ForeColor = System.Drawing.Color.White;
            this.btnDatabaseManager.Location = new System.Drawing.Point(-100, 20);
            this.btnDatabaseManager.Name = "btnDatabaseManager";
            this.btnDatabaseManager.Size = new System.Drawing.Size(150, 40);
            this.btnDatabaseManager.TabIndex = 6;
            this.btnDatabaseManager.Text = "إدارة قاعدة البيانات";
            this.btnDatabaseManager.Click += new System.EventHandler(this.btnDatabaseManager_Click);
            //
            // panelMain
            // 
            this.panelMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelMain.Location = new System.Drawing.Point(0, 80);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new System.Drawing.Size(1200, 620);
            this.panelMain.TabIndex = 1;
            this.panelMain.Paint += new System.Windows.Forms.PaintEventHandler(this.panelMain_Paint);
            // 
            // TestForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.panelMain);
            this.Controls.Add(this.guna2Panel1);
            this.Name = "TestForm";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "نظام إدارة الصيدليات";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.guna2Panel1.ResumeLayout(false);
            this.guna2Panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        private System.ComponentModel.IContainer components = null;
        private Guna2Panel guna2Panel1;
        private Guna2Button btnAddPharmacy;
        private Guna2Button btnPharmacyList;
        private Guna2Button btnPharmacyUsers;
        private Guna2Button btnSubscriptions;
        private Guna2Button btnDatabaseManager;
        private Guna2Button btnTestDatabase;
        private Guna2Button btnLogout;
        private Label lblWelcome;
        private Panel panelMain;

        private void btnAddPharmacy_Click(object sender, EventArgs e)
        {
            try
            {
                // Add debug message
                System.Diagnostics.Debug.WriteLine("btnAddPharmacy_Click called");

                ShowAddPharmacy();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة إضافة الصيدلية: {ex.Message}", "خطأ",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnPharmacyList_Click(object sender, EventArgs e)
        {
            ShowPharmacyList();
        }

        private void btnPharmacyUsers_Click(object sender, EventArgs e)
        {
            ShowPharmacyUsers();
        }

        private void btnSubscriptions_Click(object sender, EventArgs e)
        {
            ShowSubscriptionManagement();
        }

        private void btnDatabaseManager_Click(object sender, EventArgs e)
        {
            ShowDatabaseManager();
        }

        private void btnTestDatabase_Click(object sender, EventArgs e)
        {
            // Show menu for database tests
            ContextMenuStrip testMenu = new ContextMenuStrip();
            testMenu.RightToLeft = RightToLeft.Yes;

            ToolStripMenuItem testConnection = new ToolStripMenuItem("اختبار الاتصال");
            testConnection.Click += (s, args) => DatabaseTest.TestConnections();

            ToolStripMenuItem testOperations = new ToolStripMenuItem("اختبار العمليات");
            testOperations.Click += (s, args) => DatabaseTest.TestPharmacyOperations();

            ToolStripMenuItem showInfo = new ToolStripMenuItem("معلومات قاعدة البيانات");
            showInfo.Click += (s, args) => DatabaseTest.ShowDatabaseInfo();

            ToolStripMenuItem quickTest = new ToolStripMenuItem("اختبار سريع شامل");
            quickTest.Click += (s, args) => QuickTest.RunDatabaseTest();

            ToolStripMenuItem showData = new ToolStripMenuItem("عرض بيانات الصيدليات");
            showData.Click += (s, args) => QuickTest.ShowPharmacyData();

            testMenu.Items.AddRange(new ToolStripItem[] { testConnection, testOperations, showInfo, quickTest, showData });
            testMenu.Show(btnTestDatabase, new Point(0, btnTestDatabase.Height));
        }

        private void btnLogout_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                AdminUserManager.Logout();
                this.Close();
                
                // Show login form again
                LoginForm loginForm = new LoginForm();
                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    TestForm newForm = new TestForm();
                    newForm.Show();
                }
                else
                {
                    Application.Exit();
                }
            }
        }

        private void ShowAddPharmacy()
        {
            try
            {
                // Clear existing controls
                panelMain.Controls.Clear();

                // Show loading message
                this.Cursor = Cursors.WaitCursor;

                // Create new add pharmacy control
                addPharmacyControl = new add_pharmacy();
                addPharmacyControl.Dock = DockStyle.Fill;

                // Add to panel
                panelMain.Controls.Add(addPharmacyControl);

                // Reset cursor
                this.Cursor = Cursors.Default;

                // Debug message
                System.Diagnostics.Debug.WriteLine("Add pharmacy control loaded successfully");
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                MessageBox.Show($"خطأ في تحميل صفحة إضافة الصيدلية: {ex.Message}\n\n" +
                              "تحقق من:\n" +
                              "1. اتصال قاعدة البيانات\n" +
                              "2. وجود قاعدة البيانات UnifiedPharmacy\n" +
                              "3. تنفيذ ملفات الإعداد",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowPharmacyList()
        {
            try
            {
                // Clear existing controls
                panelMain.Controls.Clear();

                // Show loading message
                this.Cursor = Cursors.WaitCursor;

                // Create new pharmacy list control
                pharmacyListControl = new uc_pharmacylist();
                pharmacyListControl.Dock = DockStyle.Fill;

                // Add to panel
                panelMain.Controls.Add(pharmacyListControl);

                // Reset cursor
                this.Cursor = Cursors.Default;

                // Debug message
                System.Diagnostics.Debug.WriteLine("Pharmacy list control loaded successfully");
            }
            catch (Exception ex)
            {
                this.Cursor = Cursors.Default;
                MessageBox.Show($"خطأ في تحميل قائمة الصيدليات: {ex.Message}\n\n" +
                              "تحقق من:\n" +
                              "1. اتصال قاعدة البيانات\n" +
                              "2. وجود قاعدة البيانات UnifiedPharmacy\n" +
                              "3. وجود جدول pharmacies",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowPharmacyUsers()
        {
            try
            {
                // Clear existing controls
                panelMain.Controls.Clear();

                // Show loading message
                this.Cursor = Cursors.WaitCursor;

                // Test data retrieval first (for debugging)
                // QuickTestUsers.TestAll();

                // Create new pharmacy users control
                pharmacyUsersControl = new PharmacyUsersControl();
                pharmacyUsersControl.Dock = DockStyle.Fill;

                // Add to panel
                panelMain.Controls.Add(pharmacyUsersControl);

                // Reset cursor
                this.Cursor = Cursors.Default;

                // Debug message
                System.Diagnostics.Debug.WriteLine("Pharmacy users control loaded successfully");
            }
            catch (Exception ex)
            {
                // Reset cursor
                this.Cursor = Cursors.Default;
                MessageBox.Show($"خطأ في تحميل صفحة حسابات الصيدليات: {ex.Message}\n\n" +
                              $"تفاصيل الخطأ: {ex.StackTrace}\n\n" +
                              "تحقق من:\n" +
                              "1. اتصال قاعدة البيانات\n" +
                              "2. وجود قاعدة البيانات UnifiedPharmacy\n" +
                              "3. وجود جداول المستخدمين",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowSubscriptionManagement()
        {
            try
            {
                // Clear existing controls
                panelMain.Controls.Clear();

                // Show loading message
                this.Cursor = Cursors.WaitCursor;

                // Create new subscription management control
                subscriptionManagementControl = new forma.SubscriptionManagementControl();
                subscriptionManagementControl.Dock = DockStyle.Fill;

                // Add to panel
                panelMain.Controls.Add(subscriptionManagementControl);

                // Reset cursor
                this.Cursor = Cursors.Default;

                // Debug message
                System.Diagnostics.Debug.WriteLine("Subscription management control loaded successfully");
            }
            catch (Exception ex)
            {
                // Reset cursor
                this.Cursor = Cursors.Default;
                MessageBox.Show($"خطأ في تحميل صفحة إدارة الاشتراكات: {ex.Message}\n\n" +
                              $"تفاصيل الخطأ: {ex.StackTrace}\n\n" +
                              "تحقق من:\n" +
                              "1. اتصال قاعدة البيانات\n" +
                              "2. وجود قاعدة البيانات UnifiedPharmacy\n" +
                              "3. وجود جدول الصيدليات",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowDatabaseManager()
        {
            try
            {
                // Clear existing controls
                panelMain.Controls.Clear();

                // Show loading message
                this.Cursor = Cursors.WaitCursor;

                // Create new database manager control
                databaseManagerControl = new DatabaseManagerControl();
                databaseManagerControl.Dock = DockStyle.Fill;

                // Add to panel
                panelMain.Controls.Add(databaseManagerControl);

                // Reset cursor
                this.Cursor = Cursors.Default;

                // Debug message
                System.Diagnostics.Debug.WriteLine("Database manager control loaded successfully");
            }
            catch (Exception ex)
            {
                // Reset cursor
                this.Cursor = Cursors.Default;
                MessageBox.Show($"خطأ في تحميل صفحة إدارة قاعدة البيانات: {ex.Message}\n\n" +
                              "تحقق من:\n" +
                              "1. اتصال قاعدة البيانات\n" +
                              "2. وجود قاعدة البيانات UnifiedPharmacy\n" +
                              "3. وجود الجداول المطلوبة",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void panelMain_Paint(object sender, PaintEventArgs e)
        {

        }
    }
}
