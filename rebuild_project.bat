@echo off
echo ========================================
echo    إعادة بناء مشروع admino
echo ========================================
echo.

REM Clean temporary files
echo تنظيف الملفات المؤقتة...
if exist obj rmdir /s /q obj 2>nul
if exist bin\Debug\*.pdb del /q bin\Debug\*.pdb 2>nul
echo تم تنظيف الملفات المؤقتة.
echo.

REM Check if application already works
echo فحص حالة التطبيق الحالية...
if exist "bin\Debug\admino.exe" (
    echo التطبيق موجود، جاري اختبار التشغيل...
    start /wait /min bin\Debug\admino.exe
    if %ERRORLEVEL% EQU 0 (
        echo ✅ التطبيق يعمل بنجاح!
        echo.
        echo يمكنك تشغيل التطبيق بالأمر: .\bin\Debug\admino.exe
        echo أو انقر نقراً مزدوجاً على: bin\Debug\admino.exe
        echo.
        goto :success
    )
)

REM Try different MSBuild paths
echo البحث عن MSBuild...

REM Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2022 Professional MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" admino.csproj /p:Configuration=Debug /t:Rebuild
    goto :check_result
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2022 Community MSBuild...
    "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" admino.csproj /p:Configuration=Debug /t:Rebuild
    goto :check_result
)

REM Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2019 Professional MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" admino.csproj /p:Configuration=Debug /t:Rebuild
    goto :check_result
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo استخدام Visual Studio 2019 Community MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" admino.csproj /p:Configuration=Debug /t:Rebuild
    goto :check_result
)

echo ⚠️  لم يتم العثور على MSBuild.
echo يرجى بناء المشروع من داخل Visual Studio.
echo.
goto :manual_instructions

:check_result
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح!
    goto :success
) else (
    echo ❌ فشل في بناء المشروع.
    goto :manual_instructions
)

:success
echo.
echo ========================================
echo           تم بنجاح! 🎉
echo ========================================
echo.
echo التطبيق جاهز للاستخدام:
echo   .\bin\Debug\admino.exe
echo.
goto :end

:manual_instructions
echo.
echo ========================================
echo      تعليمات البناء اليدوي
echo ========================================
echo.
echo 1. افتح Visual Studio
echo 2. افتح المشروع: admino.sln
echo 3. اختر Build → Rebuild Solution
echo 4. أو اضغط Ctrl+Shift+B
echo.
echo إذا واجهت أخطاء في Visual Studio:
echo 1. أغلق Visual Studio
echo 2. احذف مجلد obj
echo 3. أعد فتح المشروع
echo.

:end
echo اضغط أي مفتاح للخروج...
pause >nul
