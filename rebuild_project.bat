@echo off
echo Rebuilding admino project...
echo.

REM Clean obj and bin directories
if exist obj rmdir /s /q obj
if exist bin\Debug\*.pdb del /q bin\Debug\*.pdb

echo Cleaned temporary files.
echo.

REM Try to build using MSBuild if available
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    echo Using Visual Studio 2019 Professional MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" admino.csproj /p:Configuration=Debug
    goto :end
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    echo Using Visual Studio 2019 Community MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" admino.csproj /p:Configuration=Debug
    goto :end
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    echo Using Visual Studio 2019 Build Tools MSBuild...
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" admino.csproj /p:Configuration=Debug
    goto :end
)

echo MSBuild not found. Project may need to be built from Visual Studio.
echo.

:end
echo.
echo Build process completed.
echo You can now run the application with: .\bin\Debug\admino.exe
pause
