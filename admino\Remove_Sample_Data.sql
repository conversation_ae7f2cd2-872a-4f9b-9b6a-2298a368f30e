-- =============================================
-- حذف البيانات التجريبية من جدول الصيدليات
-- Remove Sample/Test Data from Pharmacies Table
-- =============================================

USE UnifiedPharmacy;
GO

PRINT 'بدء حذف البيانات التجريبية...';

-- حذف الصيدليات التجريبية المعروفة
DELETE FROM pharmacies 
WHERE pharmacyNameAr IN (
    'صيدلية النور',
    'صيدلية الشفاء', 
    'صيدلية الحياة',
    'صيدلية الأمل',
    'صيدلية السلام'
) 
OR pharmacyName IN (
    'Al Noor Pharmacy',
    'Al Shifa Pharmacy',
    'Al Hayat Pharmacy',
    'Al Amal Pharmacy',
    'Al Salam Pharmacy'
)
OR licenseNumber IN (
    'LIC001',
    'LIC002', 
    'LIC003',
    'LIC004',
    'LIC005'
)
OR pharmacyCode IN (
    'PHM001',
    'PHM002',
    'PHM003', 
    'PHM004',
    'PHM005'
);

PRINT 'تم حذف البيانات التجريبية بنجاح';

-- عرض عدد الصيدليات المتبقية
SELECT COUNT(*) as 'عدد الصيدليات المتبقية' FROM pharmacies;

-- عرض الصيدليات المتبقية
SELECT 
    id,
    pharmacyCode,
    pharmacyNameAr,
    subscriptionStatus,
    registrationDate,
    status
FROM pharmacies
ORDER BY registrationDate DESC;

PRINT 'تم الانتهاء من تنظيف البيانات';
