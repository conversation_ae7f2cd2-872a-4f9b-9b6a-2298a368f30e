using System;
using System.Windows.Forms;

namespace admino
{
    public class TestSubscriptionProgram
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // Test database connection first
                MessageBox.Show("اختبار الاتصال بقاعدة البيانات...", "اختبار", 
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                bool adminConnected = DatabaseConnection.TestAdminConnection();
                bool pharmacyConnected = DatabaseConnection.TestPharmacyConnection();
                
                string connectionStatus = $"الاتصال بقاعدة البيانات المركزية: {(adminConnected ? "نجح" : "فشل")}\n" +
                                        $"الاتصال بقاعدة بيانات الصيدلية: {(pharmacyConnected ? "نجح" : "فشل")}";
                
                MessageBox.Show(connectionStatus, "حالة الاتصال", 
                              MessageBoxButtons.OK, 
                              (adminConnected && pharmacyConnected) ? MessageBoxIcon.Information : MessageBoxIcon.Warning);
                
                // Run the subscription form
                Application.Run(new TestSimpleSubscriptionForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التطبيق: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}", 
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
