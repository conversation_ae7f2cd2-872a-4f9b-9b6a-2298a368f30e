using System;

namespace admino
{
    public class PharmacyUser
    {
        public int Id { get; set; }
        public int PharmacyId { get; set; }
        
        // Login Information
        public string Username { get; set; }
        public string Password { get; set; }
        
        // Personal Information
        public string FullName { get; set; }
        public string FullNameAr { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public string Mobile { get; set; }
        public DateTime? DateOfBirth { get; set; }
        
        // Role and Permissions
        public int RoleId { get; set; }
        public string RoleName { get; set; }
        public string RoleNameAr { get; set; }
        
        // Status Information
        public bool IsActive { get; set; } = true;
        public DateTime? LastLogin { get; set; }
        public int LoginAttempts { get; set; } = 0;
        public DateTime? LockedUntil { get; set; }
        
        // Audit Information
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }
        
        // Additional Information
        public string ProfileImage { get; set; }
        public string Notes { get; set; }
        
        // Session Information
        public string SessionToken { get; set; }
        public DateTime? SessionExpiry { get; set; }
        
        // Pharmacy Information (for display)
        public string PharmacyName { get; set; }
        public string PharmacyNameAr { get; set; }
        public string PharmacyCode { get; set; }
        
        // Constructor
        public PharmacyUser()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
            LoginAttempts = 0;
        }
        
        // Constructor with basic information
        public PharmacyUser(int pharmacyId, string username, string password, string fullName, 
                           string fullNameAr, string email, int roleId)
        {
            PharmacyId = pharmacyId;
            Username = username;
            Password = password;
            FullName = fullName;
            FullNameAr = fullNameAr;
            Email = email;
            RoleId = roleId;
            CreatedDate = DateTime.Now;
            IsActive = true;
            LoginAttempts = 0;
        }
        
        // Validation method
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Username) &&
                   !string.IsNullOrWhiteSpace(Password) &&
                   !string.IsNullOrWhiteSpace(FullName) &&
                   !string.IsNullOrWhiteSpace(FullNameAr) &&
                   !string.IsNullOrWhiteSpace(Email) &&
                   PharmacyId > 0 &&
                   RoleId > 0;
        }
        
        // Display name for UI
        public string DisplayName => $"{FullNameAr} ({FullName})";
        
        // Full display name with role
        public string FullDisplayName => $"{FullNameAr} - {RoleNameAr}";
        
        // Status display in Arabic
        public string StatusAr
        {
            get
            {
                if (!IsActive) return "غير نشط";
                if (LockedUntil.HasValue && LockedUntil.Value > DateTime.Now) return "مقفل";
                return "نشط";
            }
        }
        
        // Last login display
        public string LastLoginDisplay
        {
            get
            {
                if (!LastLogin.HasValue) return "لم يسجل دخول من قبل";
                
                var timeSpan = DateTime.Now - LastLogin.Value;
                if (timeSpan.TotalMinutes < 1) return "منذ لحظات";
                if (timeSpan.TotalHours < 1) return $"منذ {(int)timeSpan.TotalMinutes} دقيقة";
                if (timeSpan.TotalDays < 1) return $"منذ {(int)timeSpan.TotalHours} ساعة";
                if (timeSpan.TotalDays < 30) return $"منذ {(int)timeSpan.TotalDays} يوم";
                
                return LastLogin.Value.ToString("dd/MM/yyyy");
            }
        }
        
        // Check if account is locked
        public bool IsLocked
        {
            get
            {
                return LockedUntil.HasValue && LockedUntil.Value > DateTime.Now;
            }
        }
        
        // Check if session is valid
        public bool IsSessionValid
        {
            get
            {
                return !string.IsNullOrEmpty(SessionToken) && 
                       SessionExpiry.HasValue && 
                       SessionExpiry.Value > DateTime.Now;
            }
        }
        
        // Generate session token
        public void GenerateSessionToken()
        {
            SessionToken = Guid.NewGuid().ToString("N");
            SessionExpiry = DateTime.Now.AddHours(8); // 8 hours session
        }
        
        // Clear session
        public void ClearSession()
        {
            SessionToken = null;
            SessionExpiry = null;
        }
        
        // Override ToString for debugging
        public override string ToString()
        {
            return $"{FullNameAr} ({Username}) - {RoleNameAr} - {PharmacyNameAr}";
        }
    }
    
    // User Role class
    public class UserRole
    {
        public int Id { get; set; }
        public string RoleName { get; set; }
        public string RoleNameAr { get; set; }
        public string Description { get; set; }
        public string DescriptionAr { get; set; }
        public string Permissions { get; set; } // JSON format
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public int? CreatedBy { get; set; }
        
        // Constructor
        public UserRole()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
        }
        
        // Display name for UI
        public string DisplayName => $"{RoleNameAr} ({RoleName})";
        
        // Override ToString for debugging
        public override string ToString()
        {
            return DisplayName;
        }
    }
    
    // User Permission class
    public class UserPermission
    {
        public int Id { get; set; }
        public string PermissionName { get; set; }
        public string PermissionNameAr { get; set; }
        public string Description { get; set; }
        public string DescriptionAr { get; set; }
        public string Category { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        // Constructor
        public UserPermission()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
        }
        
        // Display name for UI
        public string DisplayName => $"{PermissionNameAr} ({PermissionName})";
        
        // Override ToString for debugging
        public override string ToString()
        {
            return DisplayName;
        }
    }
}
