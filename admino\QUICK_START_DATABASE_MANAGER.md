# 🚀 دليل البدء السريع - إدارة قاعدة البيانات

## ⚡ البدء السريع

### 1. **تشغيل التطبيق**
```
F5 في Visual Studio أو تشغيل admino.exe
```

### 2. **تسجيل الدخول**
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `Admin@123`

### 3. **الوصول لإدارة قاعدة البيانات**
- انقر على زر **"إدارة قاعدة البيانات"** في الشريط العلوي (اللون البنفسجي)

## 🎯 الاستخدام السريع

### **اختيار الجدول:**
1. من القائمة المنسدلة في الأعلى، اختر الجدول المراد إدارته
2. ستظهر البيانات تلقائياً في الجدول

### **البحث:**
- اكتب في مربع البحث للبحث في جميع الحقول
- البحث فوري ولا يحتاج للضغط على Enter

### **إضافة سجل جديد:**
1. انقر زر **"إضافة جديد"** (أخضر)
2. املأ الحقول المطلوبة (المميزة بـ *)
3. انقر **"حفظ"**

### **تعديل سجل:**
1. انقر على السجل المراد تعديله
2. انقر زر **"تعديل"** (أصفر) أو انقر نقرة مزدوجة على السجل
3. عدل البيانات
4. انقر **"حفظ"**

### **حذف سجل:**
1. انقر على السجل المراد حذفه
2. انقر زر **"حذف"** (أحمر)
3. أكد الحذف

### **تحديث البيانات:**
- انقر زر **"تحديث"** (أخضر) لإعادة تحميل البيانات

## 📋 الجداول المتاحة

| الجدول | الوصف | الاستخدام |
|--------|--------|-----------|
| **الصيدليات** | معلومات الصيدليات | إدارة بيانات الصيدليات المسجلة |
| **مستخدمو الصيدليات** | حسابات المستخدمين | إدارة حسابات موظفي الصيدليات |
| **أدوار المستخدمين** | أدوار النظام | تعريف أدوار مثل مدير، صيدلي، إلخ |
| **صلاحيات المستخدمين** | صلاحيات النظام | تعريف الصلاحيات المختلفة |
| **ربط المستخدمين بالصلاحيات** | ربط الصلاحيات | منح صلاحيات للمستخدمين |
| **جلسات المستخدمين** | جلسات تسجيل الدخول | مراقبة جلسات المستخدمين |
| **سجل الأنشطة** | سجل العمليات | مراجعة جميع العمليات المنفذة |

## ⚠️ نصائح مهمة

### **قبل البدء:**
- ✅ تأكد من تشغيل SQL Server
- ✅ تأكد من وجود قاعدة البيانات `UnifiedPharmacy`
- ✅ نفذ ملف `SETUP_COMPLETE_PHARMACY_SYSTEM.sql` إذا لم تكن منفذة

### **أثناء الاستخدام:**
- 🔍 استخدم البحث للعثور على السجلات بسرعة
- 💾 احفظ البيانات بانتظام
- 🗑️ كن حذراً عند الحذف - لا يمكن التراجع!
- 🔄 حدث البيانات إذا كان هناك مستخدمون آخرون

### **الحقول المطلوبة:**
- الحقول المميزة بـ ***** مطلوبة
- لا يمكن حفظ السجل بدونها
- ستظهر رسالة خطأ إذا كانت فارغة

## 🛠️ استكشاف الأخطاء

### **لا تظهر البيانات:**
1. تحقق من اتصال قاعدة البيانات
2. تحقق من وجود الجداول
3. انقر زر "تحديث"

### **خطأ في الحفظ:**
1. تحقق من ملء الحقول المطلوبة
2. تحقق من صحة البيانات (تواريخ، أرقام، إلخ)
3. تحقق من عدم تكرار البيانات الفريدة

### **خطأ في الحذف:**
1. تحقق من عدم وجود بيانات مرتبطة في جداول أخرى
2. بعض السجلات لا يمكن حذفها لأسباب أمنية

## 🎨 واجهة المستخدم

### **الألوان:**
- 🟢 **أخضر** = إضافة/تحديث
- 🟡 **أصفر** = تعديل
- 🔴 **أحمر** = حذف
- 🟣 **بنفسجي** = إدارة قاعدة البيانات

### **الرموز:**
- 📊 عداد السجلات في الأعلى
- 🔍 مربع البحث
- 📋 قائمة الجداول
- 🗂️ جدول البيانات

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `DATABASE_MANAGER_README.md` للتفاصيل الكاملة
2. تحقق من ملفات الإعداد SQL
3. راجع رسائل الخطأ بعناية

---

## ✅ **جاهز للاستخدام!**

النظام جاهز الآن لإدارة قاعدة البيانات بكل سهولة! 🎉
