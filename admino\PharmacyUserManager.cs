using System;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Linq;

namespace admino
{
    public static class PharmacyUserManager
    {
        public static PharmacyUser CurrentUser { get; private set; }

        // =============================================
        // Authentication Methods
        // =============================================
        
        // Login method
        public static bool Login(string username, string password, string ipAddress = "", string userAgent = "")
        {
            try
            {
                string query = @"
                    SELECT pu.id, pu.pharmacyId, pu.username, pu.password, pu.fullName, pu.fullNameAr, 
                           pu.email, pu.phone, pu.mobile, pu.roleId, pu.isActive, pu.loginAttempts, 
                           pu.lockedUntil, pu.lastLogin,
                           ur.roleName, ur.roleNameAr, ur.permissions,
                           p.pharmacyName, p.pharmacyNameAr, p.pharmacyCode
                    FROM pharmacy_users pu
                    INNER JOIN user_roles ur ON pu.roleId = ur.id
                    INNER JOIN pharmacies p ON pu.pharmacyId = p.id
                    WHERE pu.username = @username AND pu.isActive = 1 AND p.isActive = 1";

                SqlParameter[] parameters = {
                    new SqlParameter("@username", username)
                };

                DataTable result = DatabaseConnection.ExecutePharmacyQuery(query, parameters);

                if (result.Rows.Count == 0)
                {
                    LogLoginAttempt(username, "Failed", "المستخدم غير موجود", ipAddress, userAgent);
                    return false;
                }

                DataRow userRow = result.Rows[0];
                
                // Check if account is locked
                if (userRow["lockedUntil"] != DBNull.Value)
                {
                    DateTime lockedUntil = Convert.ToDateTime(userRow["lockedUntil"]);
                    if (DateTime.Now < lockedUntil)
                    {
                        LogLoginAttempt(username, "Blocked", "الحساب مقفل", ipAddress, userAgent);
                        return false;
                    }
                }

                // Verify password
                string storedPassword = userRow["password"].ToString();
                if (!VerifyPassword(password, storedPassword))
                {
                    // Increment login attempts
                    int loginAttempts = Convert.ToInt32(userRow["loginAttempts"]) + 1;
                    UpdateLoginAttempts(username, loginAttempts);
                    
                    LogLoginAttempt(username, "Failed", "كلمة مرور خاطئة", ipAddress, userAgent);
                    return false;
                }

                // Successful login
                int userId = Convert.ToInt32(userRow["id"]);
                
                // Reset login attempts and update last login
                ResetLoginAttempts(username);
                UpdateLastLogin(userId);

                // Create current user object
                CurrentUser = new PharmacyUser
                {
                    Id = userId,
                    PharmacyId = Convert.ToInt32(userRow["pharmacyId"]),
                    Username = username,
                    FullName = userRow["fullName"].ToString(),
                    FullNameAr = userRow["fullNameAr"].ToString(),
                    Email = userRow["email"].ToString(),
                    Phone = userRow["phone"]?.ToString(),
                    Mobile = userRow["mobile"]?.ToString(),
                    RoleId = Convert.ToInt32(userRow["roleId"]),
                    RoleName = userRow["roleName"].ToString(),
                    RoleNameAr = userRow["roleNameAr"].ToString(),
                    LastLogin = DateTime.Now,
                    PharmacyName = userRow["pharmacyName"].ToString(),
                    PharmacyNameAr = userRow["pharmacyNameAr"].ToString(),
                    PharmacyCode = userRow["pharmacyCode"].ToString()
                };

                // Generate session token
                CurrentUser.GenerateSessionToken();
                UpdateSessionToken(userId, CurrentUser.SessionToken, CurrentUser.SessionExpiry.Value);

                // Log successful login
                LogLoginAttempt(username, "Success", "تسجيل دخول ناجح", ipAddress, userAgent, userId);
                LogActivity(userId, "LOGIN", "تسجيل دخول", "pharmacy_users", userId, 
                           "User logged in", "قام المستخدم بتسجيل الدخول", ipAddress, userAgent);

                return true;
            }
            catch (Exception ex)
            {
                LogLoginAttempt(username, "Failed", $"خطأ في النظام: {ex.Message}", ipAddress, userAgent);
                throw new Exception($"خطأ في تسجيل الدخول: {ex.Message}");
            }
        }

        // Logout method
        public static void Logout()
        {
            if (CurrentUser != null)
            {
                LogActivity(CurrentUser.Id, "LOGOUT", "تسجيل خروج", "pharmacy_users", CurrentUser.Id,
                           "User logged out", "قام المستخدم بتسجيل الخروج");
                
                // Clear session from database
                ClearSessionToken(CurrentUser.Id);
                
                CurrentUser = null;
            }
        }

        // =============================================
        // User Management Methods
        // =============================================
        
        // Get all pharmacy users
        public static DataTable GetAllPharmacyUsers()
        {
            try
            {
                string query = @"
                    SELECT u.id,
                           u.pharmacyId,
                           u.username,
                           u.name as fullName,
                           u.name as fullNameAr,
                           ISNULL(u.email, '') as email,
                           '' as phone,
                           CAST(ISNULL(u.mobile, 0) as NVARCHAR(20)) as mobile,
                           ISNULL(u.isActive, 1) as isActive,
                           u.lastLogin,
                           ISNULL(u.createdAt, GETDATE()) as createdDate,
                           0 as loginAttempts,
                           NULL as lockedUntil,
                           ISNULL(u.userRole, 'Employee') as roleName,
                           ISNULL(u.userRole, 'Employee') as roleNameAr,
                           ISNULL(p.pharmacyName, 'Unknown Pharmacy') as pharmacyName,
                           ISNULL(p.pharmacyNameAr, 'صيدلية غير محددة') as pharmacyNameAr,
                           ISNULL(p.pharmacyCode, 'N/A') as pharmacyCode
                    FROM users u
                    LEFT JOIN pharmacies p ON u.pharmacyId = p.id
                    ORDER BY u.id";

                return DatabaseConnection.ExecutePharmacyQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب بيانات المستخدمين: {ex.Message}");
            }
        }

        // Get users by pharmacy
        public static DataTable GetUsersByPharmacy(int pharmacyId)
        {
            try
            {
                string query = @"
                    SELECT pu.id, pu.username, pu.fullName, pu.fullNameAr, 
                           pu.email, pu.phone, pu.mobile, pu.isActive, pu.lastLogin, 
                           pu.createdDate, pu.loginAttempts, pu.lockedUntil,
                           ur.roleName, ur.roleNameAr
                    FROM pharmacy_users pu
                    INNER JOIN user_roles ur ON pu.roleId = ur.id
                    WHERE pu.pharmacyId = @pharmacyId
                    ORDER BY ur.roleName, pu.fullNameAr";

                SqlParameter[] parameters = {
                    new SqlParameter("@pharmacyId", pharmacyId)
                };

                return DatabaseConnection.ExecutePharmacyQuery(query, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب مستخدمي الصيدلية: {ex.Message}");
            }
        }

        // Add new pharmacy user
        public static bool AddPharmacyUser(PharmacyUser user)
        {
            try
            {
                string query = @"
                    INSERT INTO pharmacy_users
                    (pharmacyId, username, password, fullName, fullNameAr, email, phone, mobile,
                     roleId, isActive, createdDate, createdBy, notes)
                    VALUES
                    (@pharmacyId, @username, @password, @fullName, @fullNameAr, @email, @phone, @mobile,
                     @roleId, @isActive, @createdDate, @createdBy, @notes)";

                SqlParameter[] parameters = {
                    new SqlParameter("@pharmacyId", user.PharmacyId),
                    new SqlParameter("@username", user.Username ?? ""),
                    new SqlParameter("@password", user.Password ?? ""),
                    new SqlParameter("@fullName", user.FullName ?? ""),
                    new SqlParameter("@fullNameAr", user.FullNameAr ?? ""),
                    new SqlParameter("@email", user.Email ?? ""),
                    new SqlParameter("@phone", user.Phone ?? ""),
                    new SqlParameter("@mobile", user.Mobile ?? ""),
                    new SqlParameter("@roleId", user.RoleId),
                    new SqlParameter("@isActive", user.IsActive),
                    new SqlParameter("@createdDate", user.CreatedDate),
                    new SqlParameter("@createdBy", (object)user.CreatedBy ?? DBNull.Value),
                    new SqlParameter("@notes", user.Notes ?? "")
                };

                int result = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);

                if (result > 0)
                {
                    // Log activity
                    LogActivity(CurrentUser?.Id, "CREATE", "إضافة مستخدم", "pharmacy_users", null,
                               $"Added new user: {user.Username}", $"تم إضافة مستخدم جديد: {user.FullNameAr}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة المستخدم: {ex.Message}");
            }
        }

        // Update pharmacy user
        public static bool UpdatePharmacyUser(PharmacyUser user)
        {
            try
            {
                string query = @"
                    UPDATE pharmacy_users SET
                        fullName = @fullName,
                        fullNameAr = @fullNameAr,
                        email = @email,
                        phone = @phone,
                        mobile = @mobile,
                        roleId = @roleId,
                        isActive = @isActive,
                        modifiedDate = @modifiedDate,
                        modifiedBy = @modifiedBy,
                        notes = @notes
                    WHERE id = @id";

                SqlParameter[] parameters = {
                    new SqlParameter("@id", user.Id),
                    new SqlParameter("@fullName", user.FullName ?? ""),
                    new SqlParameter("@fullNameAr", user.FullNameAr ?? ""),
                    new SqlParameter("@email", user.Email ?? ""),
                    new SqlParameter("@phone", user.Phone ?? ""),
                    new SqlParameter("@mobile", user.Mobile ?? ""),
                    new SqlParameter("@roleId", user.RoleId),
                    new SqlParameter("@isActive", user.IsActive),
                    new SqlParameter("@modifiedDate", DateTime.Now),
                    new SqlParameter("@modifiedBy", (object)user.ModifiedBy ?? DBNull.Value),
                    new SqlParameter("@notes", user.Notes ?? "")
                };

                int result = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);

                if (result > 0)
                {
                    // Log activity
                    LogActivity(CurrentUser?.Id, "UPDATE", "تحديث مستخدم", "pharmacy_users", user.Id,
                               $"Updated user: {user.Username}", $"تم تحديث المستخدم: {user.FullNameAr}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المستخدم: {ex.Message}");
            }
        }

        // Delete pharmacy user
        public static bool DeletePharmacyUser(int userId)
        {
            try
            {
                string query = "DELETE FROM pharmacy_users WHERE id = @id";
                SqlParameter[] parameters = {
                    new SqlParameter("@id", userId)
                };

                int result = DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);

                if (result > 0)
                {
                    LogActivity(CurrentUser?.Id, "DELETE", "حذف مستخدم", "pharmacy_users", userId,
                               $"Deleted user ID: {userId}", $"تم حذف المستخدم رقم: {userId}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف المستخدم: {ex.Message}");
            }
        }

        // Get all user roles
        public static DataTable GetAllUserRoles()
        {
            try
            {
                string query = @"
                    SELECT DISTINCT
                           userRole as roleName,
                           userRole as roleNameAr
                    FROM users
                    WHERE userRole IS NOT NULL AND userRole != ''
                    ORDER BY userRole";

                return DatabaseConnection.ExecutePharmacyQuery(query);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الأدوار: {ex.Message}");
            }
        }

        // =============================================
        // Helper Methods
        // =============================================

        private static bool VerifyPassword(string inputPassword, string storedPassword)
        {
            // Simple comparison for now - in production, use proper hashing
            return inputPassword == storedPassword;
        }

        private static void UpdateLoginAttempts(string username, int attempts)
        {
            try
            {
                string query = @"
                    UPDATE pharmacy_users SET
                        loginAttempts = @attempts,
                        lockedUntil = CASE WHEN @attempts >= 5 THEN DATEADD(MINUTE, 30, GETDATE()) ELSE NULL END
                    WHERE username = @username";

                SqlParameter[] parameters = {
                    new SqlParameter("@username", username),
                    new SqlParameter("@attempts", attempts)
                };

                DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);
            }
            catch { }
        }

        private static void ResetLoginAttempts(string username)
        {
            try
            {
                string query = @"
                    UPDATE pharmacy_users SET
                        loginAttempts = 0,
                        lockedUntil = NULL
                    WHERE username = @username";

                SqlParameter[] parameters = {
                    new SqlParameter("@username", username)
                };

                DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);
            }
            catch { }
        }

        private static void UpdateLastLogin(int userId)
        {
            try
            {
                string query = "UPDATE pharmacy_users SET lastLogin = GETDATE() WHERE id = @id";
                SqlParameter[] parameters = {
                    new SqlParameter("@id", userId)
                };

                DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);
            }
            catch { }
        }

        private static void UpdateSessionToken(int userId, string token, DateTime expiry)
        {
            try
            {
                string query = @"
                    UPDATE pharmacy_users SET
                        sessionToken = @token,
                        sessionExpiry = @expiry
                    WHERE id = @id";

                SqlParameter[] parameters = {
                    new SqlParameter("@id", userId),
                    new SqlParameter("@token", token),
                    new SqlParameter("@expiry", expiry)
                };

                DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);
            }
            catch { }
        }

        private static void ClearSessionToken(int userId)
        {
            try
            {
                string query = @"
                    UPDATE pharmacy_users SET
                        sessionToken = NULL,
                        sessionExpiry = NULL
                    WHERE id = @id";

                SqlParameter[] parameters = {
                    new SqlParameter("@id", userId)
                };

                DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);
            }
            catch { }
        }

        private static void LogLoginAttempt(string username, string status, string message,
                                          string ipAddress = "", string userAgent = "", int? userId = null)
        {
            try
            {
                // This would typically log to a separate login_attempts table
                // For now, we'll use the activity log
                LogActivity(userId, "LOGIN_ATTEMPT", $"محاولة تسجيل دخول - {status}",
                           "pharmacy_users", userId, $"Login attempt: {message}",
                           $"محاولة تسجيل دخول: {message}", ipAddress, userAgent);
            }
            catch { }
        }

        public static void LogActivity(int? userId, string action, string actionAr, string tableName,
                                     int? recordId, string description = "", string descriptionAr = "",
                                     string ipAddress = "", string userAgent = "")
        {
            try
            {
                string query = @"
                    INSERT INTO user_activity_log
                    (userId, pharmacyId, action, actionAr, tableName, recordId,
                     description, descriptionAr, ipAddress, userAgent, timestamp)
                    VALUES
                    (@userId, @pharmacyId, @action, @actionAr, @tableName, @recordId,
                     @description, @descriptionAr, @ipAddress, @userAgent, GETDATE())";

                SqlParameter[] parameters = {
                    new SqlParameter("@userId", (object)userId ?? DBNull.Value),
                    new SqlParameter("@pharmacyId", (object)CurrentUser?.PharmacyId ?? DBNull.Value),
                    new SqlParameter("@action", action ?? ""),
                    new SqlParameter("@actionAr", actionAr ?? ""),
                    new SqlParameter("@tableName", tableName ?? ""),
                    new SqlParameter("@recordId", (object)recordId ?? DBNull.Value),
                    new SqlParameter("@description", description ?? ""),
                    new SqlParameter("@descriptionAr", descriptionAr ?? ""),
                    new SqlParameter("@ipAddress", ipAddress ?? ""),
                    new SqlParameter("@userAgent", userAgent ?? "")
                };

                DatabaseConnection.ExecutePharmacyNonQuery(query, parameters);
            }
            catch { }
        }

        // Check if user has permission
        public static bool HasPermission(string permissionName)
        {
            if (CurrentUser == null) return false;

            try
            {
                string query = @"
                    SELECT COUNT(*)
                    FROM user_permission_mapping upm
                    INNER JOIN user_permissions up ON upm.permissionId = up.id
                    WHERE upm.userId = @userId
                    AND up.permissionName = @permissionName
                    AND upm.isActive = 1
                    AND up.isActive = 1
                    AND (upm.expiryDate IS NULL OR upm.expiryDate > GETDATE())";

                SqlParameter[] parameters = {
                    new SqlParameter("@userId", CurrentUser.Id),
                    new SqlParameter("@permissionName", permissionName)
                };

                DataTable result = DatabaseConnection.ExecutePharmacyQuery(query, parameters);
                return Convert.ToInt32(result.Rows[0][0]) > 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
