@echo off
echo ========================================
echo    تجميع مباشر لمشروع admino
echo ========================================
echo.

REM Create directories
if not exist bin\Debug mkdir bin\Debug
if not exist obj mkdir obj

echo تجميع الملفات...

REM Find C# compiler
set CSC_PATH=""
if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
    set CSC_PATH="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
) else if exist "C:\Windows\Microsoft.NET\Framework\v4.0.30319\csc.exe" (
    set CSC_PATH="C:\Windows\Microsoft.NET\Framework\v4.0.30319\csc.exe"
) else (
    echo ❌ لم يتم العثور على مجمع C#
    goto :error
)

echo استخدام المجمع: %CSC_PATH%

REM Compile
%CSC_PATH% /target:winexe ^
    /out:bin\Debug\admino.exe ^
    /reference:"packages\Guna.UI2.WinForms.2.0.4.6\lib\net40\Guna.UI2.dll" ^
    /reference:"packages\System.Data.SqlClient.4.8.6\lib\net461\System.Data.SqlClient.dll" ^
    /reference:System.dll ^
    /reference:System.Core.dll ^
    /reference:System.Data.dll ^
    /reference:System.Drawing.dll ^
    /reference:System.Windows.Forms.dll ^
    /reference:System.Xml.dll ^
    *.cs ^
    forma\*.cs ^
    Properties\*.cs

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم التجميع بنجاح!
    echo.
    echo تشغيل التطبيق...
    start bin\Debug\admino.exe
) else (
    echo ❌ فشل في التجميع
    goto :error
)

goto :end

:error
echo.
echo ========================================
echo      استخدم Visual Studio
echo ========================================
echo.
echo 1. افتح Visual Studio
echo 2. افتح admino.sln
echo 3. اختر Build → Rebuild Solution
echo 4. انقر Yes عند ظهور رسالة الخطأ
echo.

:end
pause
