{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\admino\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|c:\\users\\<USER>\\source\\repos\\admino\\admino\\testform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\testform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\addeditpharmacyuserform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\addeditpharmacyuserform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\pharmacyuserscontrol.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\pharmacyuserscontrol.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\pharmacyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\pharmacyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\pharmacyuser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\pharmacyuser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\add pharmacy.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\testform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\testform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\pharmacymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\pharmacymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_pharmacylist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\uc_pharmacylist.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_dashbord.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\uc_dashbord.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\add pharmacy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\all.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\all.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\quicktest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\quicktest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_pharmacylist.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\uc_pharmacylist.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\databasetest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\databasetest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\databaseconnection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\pharmacyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\pharmacyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\loginform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\form1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\all.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\all.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\adminusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\adminusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{89C09A08-67EE-4E93-9324-33B34C693FBE}|admino\\admino.csproj|solutionrelative:admino\\forma\\add pharmacy.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 3, "Title": "PharmacyUserManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyUserManager.cs", "RelativeDocumentMoniker": "admino\\PharmacyUserManager.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyUserManager.cs", "RelativeToolTip": "admino\\PharmacyUserManager.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T03:17:41.999Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "PharmacyUser.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyUser.cs", "RelativeDocumentMoniker": "admino\\PharmacyUser.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyUser.cs", "RelativeToolTip": "admino\\PharmacyUser.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T03:17:09.028Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "PharmacyUsersControl.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\PharmacyUsersControl.cs", "RelativeDocumentMoniker": "admino\\forma\\PharmacyUsersControl.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\PharmacyUsersControl.cs", "RelativeToolTip": "admino\\forma\\PharmacyUsersControl.cs", "ViewState": "AgIAANYCAAAAAAAAAAAAAP0CAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T02:47:31.258Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "AddEditPharmacyUserForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\AddEditPharmacyUserForm.cs", "RelativeDocumentMoniker": "admino\\forma\\AddEditPharmacyUserForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\AddEditPharmacyUserForm.cs", "RelativeToolTip": "admino\\forma\\AddEditPharmacyUserForm.cs", "ViewState": "AgIAAGwCAAAAAAAAAAAAAHgCAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T02:45:06.338Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "uc_dashbord.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_dashbord.cs", "RelativeDocumentMoniker": "admino\\forma\\uc_dashbord.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_dashbord.cs [Design]", "RelativeToolTip": "admino\\forma\\uc_dashbord.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T17:26:42.758Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "add pharmacy.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.cs", "RelativeDocumentMoniker": "admino\\forma\\add pharmacy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.cs [Design]", "RelativeToolTip": "admino\\forma\\add pharmacy.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T17:26:40.708Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "all.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\all.cs", "RelativeDocumentMoniker": "admino\\all.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\all.cs [Design]", "RelativeToolTip": "admino\\all.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T17:26:36.697Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "Form1.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\Form1.cs", "RelativeDocumentMoniker": "admino\\Form1.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\Form1.cs [Design]", "RelativeToolTip": "admino\\Form1.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T17:26:34.569Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "TestForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\TestForm.cs", "RelativeDocumentMoniker": "admino\\TestForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\TestForm.cs", "RelativeToolTip": "admino\\TestForm.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:47.599Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "QuickTest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\QuickTest.cs", "RelativeDocumentMoniker": "admino\\QuickTest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\QuickTest.cs", "RelativeToolTip": "admino\\QuickTest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:42.716Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\Program.cs", "RelativeDocumentMoniker": "admino\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\Program.cs", "RelativeToolTip": "admino\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:40.429Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "PharmacyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyManager.cs", "RelativeDocumentMoniker": "admino\\PharmacyManager.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyManager.cs", "RelativeToolTip": "admino\\PharmacyManager.cs", "ViewState": "AgIAAJQBAAAAAAAAAAAAALIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:33.579Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "PharmacyInfo.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyInfo.cs", "RelativeDocumentMoniker": "admino\\PharmacyInfo.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\PharmacyInfo.cs", "RelativeToolTip": "admino\\PharmacyInfo.cs", "ViewState": "AgIAAIwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:30.038Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "LoginForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\LoginForm.cs", "RelativeDocumentMoniker": "admino\\LoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\LoginForm.cs", "RelativeToolTip": "admino\\LoginForm.cs", "ViewState": "AgIAACYBAAAAAAAAAIAwwD4BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:24.519Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "Form1.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\Form1.cs", "RelativeDocumentMoniker": "admino\\Form1.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\Form1.cs", "RelativeToolTip": "admino\\Form1.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:17.654Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "all.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\all.cs", "RelativeDocumentMoniker": "admino\\all.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\all.cs", "RelativeToolTip": "admino\\all.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:40:02.407Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "AdminUserManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\AdminUserManager.cs", "RelativeDocumentMoniker": "admino\\AdminUserManager.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\AdminUserManager.cs", "RelativeToolTip": "admino\\AdminUserManager.cs", "ViewState": "AgIAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T15:39:53.336Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "DatabaseConnection.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\DatabaseConnection.cs", "RelativeDocumentMoniker": "admino\\DatabaseConnection.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\DatabaseConnection.cs", "RelativeToolTip": "admino\\DatabaseConnection.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T01:46:28.116Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "DatabaseTest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\DatabaseTest.cs", "RelativeDocumentMoniker": "admino\\DatabaseTest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\DatabaseTest.cs", "RelativeToolTip": "admino\\DatabaseTest.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAFwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T01:19:55.81Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "uc_pharmacylist.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_pharmacylist.Designer.cs", "RelativeDocumentMoniker": "admino\\forma\\uc_pharmacylist.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_pharmacylist.Designer.cs", "RelativeToolTip": "admino\\forma\\uc_pharmacylist.Designer.cs", "ViewState": "AgIAAE4BAAAAAAAAAAAAACkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:59:48.921Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "TestForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\TestForm.cs", "RelativeDocumentMoniker": "admino\\TestForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\TestForm.cs [Design]", "RelativeToolTip": "admino\\TestForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:58:23.37Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "LoginForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\LoginForm.cs", "RelativeDocumentMoniker": "admino\\LoginForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\LoginForm.cs [Design]", "RelativeToolTip": "admino\\LoginForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:58:10.319Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "add pharmacy.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.Designer.cs", "RelativeDocumentMoniker": "admino\\forma\\add pharmacy.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.Designer.cs", "RelativeToolTip": "admino\\forma\\add pharmacy.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:57:21.928Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "add pharmacy.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.cs", "RelativeDocumentMoniker": "admino\\forma\\add pharmacy.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\add pharmacy.cs", "RelativeToolTip": "admino\\forma\\add pharmacy.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:24:59.031Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "uc_pharmacylist.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_pharmacylist.cs", "RelativeDocumentMoniker": "admino\\forma\\uc_pharmacylist.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\admino\\admino\\forma\\uc_pharmacylist.cs [Design]", "RelativeToolTip": "admino\\forma\\uc_pharmacylist.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-02T00:20:47.135Z"}]}]}]}